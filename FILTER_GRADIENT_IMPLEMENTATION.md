# Filter Gradient Implementation

## 概述
為 `.news-list__filter` 元素在手機寬度下添加了左右側 40px 寬度的漸層效果，用來暗示用戶可以滑動查看更多內容。當滑動到底部時，相應的漸層會消失。

## 實現的功能
1. **漸層效果**: 在手機寬度下顯示左右兩側的漸層
2. **動態控制**: 根據滑動位置動態顯示/隱藏漸層
3. **響應式設計**: 在較大螢幕寬度下自動隱藏漸層
4. **深色主題支援**: 為深色主題提供相應的漸層顏色

## 修改的文件

### 主要版本 (根目錄)
- `css/main.css` - 添加漸層樣式和響應式控制
- `css/theme-dark.css` - 深色主題的漸層顏色
- `js/main.js` - JavaScript 滑動檢測和類別控制

### Liarch 版本
- `liarch/css/main.css` - 添加漸層樣式和響應式控制
- `liarch/css/theme-dark.css` - 深色主題的漸層顏色
- `liarch/js/main.js` - JavaScript 滑動檢測和類別控制

## CSS 實現細節

### 基本漸層樣式
```css
.news-list__filter::before,
.news-list__filter::after {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  width: 40px;
  pointer-events: none;
  z-index: 2;
  transition: opacity 0.3s ease;
}
```

### 漸層顏色
- **主要版本**: `rgba(254, 250, 233, 1)` 到透明
- **Liarch 版本**: `rgba(216, 213, 191, 1)` 到透明
- **深色主題**: `rgba(34, 34, 34, 1)` 到透明

### 響應式控制
- **主要版本**: 在 576px 以上隱藏漸層
- **Liarch 版本**: 在 768px 以上隱藏漸層

## JavaScript 實現細節

### 滑動檢測邏輯
1. 檢測滑動位置是否在開始位置 (≤5px)
2. 檢測滑動位置是否在結束位置 (≥maxScroll-5px)
3. 根據位置添加相應的 CSS 類別

### CSS 類別控制
- `.scroll-start`: 隱藏左側漸層
- `.scroll-end`: 隱藏右側漸層

### 事件處理
- 滑動事件監聽
- 視窗大小改變事件監聽
- 頁面載入時的初始化

## 測試文件
創建了 `test-filter-gradient.html` 用於測試功能，包含：
- 測試用的 filter 元素
- 調試信息顯示
- 使用說明

## 使用方法
1. 在手機寬度 (<576px 或 <768px) 下查看效果
2. 滑動 filter 元素查看漸層變化
3. 在開始位置時左側漸層隱藏
4. 在結束位置時右側漸層隱藏
5. 在較大螢幕寬度下漸層完全隱藏

## 瀏覽器支援
- 支援現代瀏覽器的 CSS 漸層
- 支援 touch 滑動 (-webkit-overflow-scrolling: touch)
- 使用 jQuery 進行 JavaScript 操作
