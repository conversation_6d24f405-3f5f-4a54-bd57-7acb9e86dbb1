<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Filter Gradient Test</title>
    <link rel="stylesheet" href="css/bootstrap-grid.css">
    <link rel="stylesheet" href="css/main.css">
    <script src="js/jquery-3.5.1.min.js"></script>
</head>
<body style="background-color: #fefae9;">
    <div class="container" style="padding: 50px 0;">
        <h1>Filter Gradient Test</h1>
        <p>Resize your browser to mobile width (&lt;576px) to see the gradient effect.</p>
        
        <!-- Filter-->
        <div class="filter controls news-list__filter">
            <button class="filter__item filter__item--active" type="button" data-filter="*">all</button>
            <button class="filter__item" type="button" data-filter=".__js_inspirations">inspirations</button>
            <button class="filter__item" type="button" data-filter=".__js_experiences">experiences</button>
            <button class="filter__item" type="button" data-filter=".__js_reviews">reviews</button>
            <button class="filter__item" type="button" data-filter=".__js_tips">tips</button>
            <button class="filter__item" type="button" data-filter=".__js_others">others</button>
            <button class="filter__item" type="button" data-filter=".__js_more">more items</button>
            <button class="filter__item" type="button" data-filter=".__js_extra">extra content</button>
        </div>
        
        <div style="margin-top: 50px;">
            <h2>Instructions:</h2>
            <ol>
                <li>Open browser developer tools (F12)</li>
                <li>Toggle device toolbar (Ctrl+Shift+M or Cmd+Shift+M)</li>
                <li>Set width to less than 576px (e.g., iPhone width)</li>
                <li>You should see gradient effects on left and right sides</li>
                <li>Scroll the filter horizontally to see gradients appear/disappear</li>
                <li>At the start position, left gradient should be hidden</li>
                <li>At the end position, right gradient should be hidden</li>
            </ol>

            <h3>Debug Info:</h3>
            <div id="debug-info" style="background: #f5f5f5; padding: 10px; margin: 10px 0; font-family: monospace;">
                <div>Window width: <span id="window-width"></span></div>
                <div>Filter scroll left: <span id="scroll-left"></span></div>
                <div>Filter scroll width: <span id="scroll-width"></span></div>
                <div>Filter client width: <span id="client-width"></span></div>
                <div>Max scroll: <span id="max-scroll"></span></div>
                <div>Classes: <span id="filter-classes"></span></div>
            </div>
        </div>

        <script>
            $(document).ready(function() {
                var $filter = $('.news-list__filter');
                var $debugInfo = $('#debug-info');

                function updateDebugInfo() {
                    $('#window-width').text(window.innerWidth);
                    if ($filter.length) {
                        $('#scroll-left').text($filter.scrollLeft());
                        $('#scroll-width').text($filter[0].scrollWidth);
                        $('#client-width').text($filter[0].clientWidth);
                        $('#max-scroll').text($filter[0].scrollWidth - $filter[0].clientWidth);
                        $('#filter-classes').text($filter.attr('class'));
                    }
                }

                updateDebugInfo();
                $filter.on('scroll', updateDebugInfo);
                $(window).on('resize', updateDebugInfo);

                setInterval(updateDebugInfo, 100);
            });
        </script>
    </div>
    
    <script src="js/main.js"></script>
</body>
</html>
