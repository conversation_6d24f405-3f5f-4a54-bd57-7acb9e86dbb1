/* ------------------------------------------------------------------------------

  1.  Global

      1.1 General
      1.2 Utils
      1.3 Buttons
      1.4 Fields
      1.5 Video button
      1.6 Heading
      1.7 Menu toggle button
      1.8 Header search
      1.9 Chat button
      1.10 Navigation
      1.11 Logo
      1.12 Social
      1.13 More
      1.14 More button
      1.15 Pagination
      1.16 Filter
      1.17 Sort
      1.18 Paginate
      1.19 Overlay
      1.20 Modal
      1.21 Preloader
      1.22 Webpage
      1.23 Language switcher

  2.  Header

      2.1 Header type 1
      2.2 Header type 2

  3.  Footer

      3.1 Footer type 1
      3.2 Footer type 2

  4.  Main

      4.1 Article preview
      4.2 Statistics
      4.3 Phone block
      4.4 Work card
      4.5 Slide content
      4.6 Slide
      4.7 Slider
      4.8 Project meta
      4.9 Twitter block
      4.10 Like
      4.11 Testimonials
      4.12 Simplicity
      4.13 Approach
      4.14 Reward
      4.15 Rewards
      4.16 Form
      4.17 Timer
      4.18 Contact info
      4.19 Specialization
      4.20 Process step
      4.21 Diagram
      4.22 Our journal
      4.23 Contact block
      4.24 Feedback form
      4.25 Header toggle
      4.26 Latest article
      4.27 Latest articles
      4.28 Fact
      4.29 Hero banner
      4.30 Instagram block
      4.31 Layout
      4.32 Sidebar
      4.33 Menu
      4.34 Latest projects
      4.35 Review
      4.36 Partners
      4.37 Article list
      4.38 Our story
      4.39 Type service
      4.40 Awards
      4.41 Works
      4.42 Single project
      4.43 About
      4.44 News list
      4.45 News slider
      4.46 News card
      4.47 Post with sidebar
      4.48 Post without sidebar
      4.49 Masonry
      4.50 News masonry
      4.51 News with sidebar
      4.52 Error 404
      4.53 Pricing
      4.54 Services
      4.55 Contacts
      4.56 Services page
      4.57 Team
      4.58 Launch page
      4.59 Homepage parallax
      4.60 Team item
      4.61 Our experts
      4.62 Background wrapper
      4.63 Works section
      4.64 Studio section
			4.65 Studio hero
			4.66 Technical drawing hero slider
			4.67 Awards table

-------------------------------------------------------------------------------*/
@import url(aos.css);
@import url(swiper-bundle.css);
@import url(animsition.min.css);
@import url(jquery.pagepiling.css);
@import url(jquery.fancybox.css);
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap');
/*-------------------------------------------------------------------------------
  1. Global
-------------------------------------------------------------------------------*/
/* 1.1 General */
html,
body {
  overflow: visible;
  background-color: #fefae9;
}

html {
  box-sizing: border-box;
}

html ::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

html ::-webkit-scrollbar-button {
  display: none;
}

html ::-webkit-scrollbar-track-piece {
  background-color: #eaeaea;
}

html ::-webkit-scrollbar-thumb {
  background-color: #57695a;
  border-radius: none;
}

html ::-webkit-scrollbar-corner {
  background-color: #999;
}

html ::-webkit-resizer {
  background-color: #666;
}

html.beige ::-webkit-scrollbar-thumb {
  background-color: #ffdf91;
  border-radius: none;
}

html.modern ::-webkit-scrollbar-thumb {
  background-color: #57695a;
  border-radius: none;
}

*,
*::before,
*::after {
  box-sizing: inherit;
}

body {
  display: flex;
  flex-direction: column;
  min-width: 320px;
  min-height: 100vh;
  margin: 0;
  padding: 0;
  font-family: "Montserrat", "Arial", sans-serif;
  font-size: 18px;
  line-height: 1.5;
  color: #000000;
  font-weight: 400;
  -webkit-locale: auto;
  white-space: normal;
}

main {
  position: relative;
  z-index: 1;
  flex-grow: 1;
  background-color: #fefae9;
}

a {
  text-decoration: none;
}

a:hover, a:focus {
  text-decoration: none;
}

input,
select,
textarea {
  background-color: transparent;
  border: 0;
  border-radius: 0;
  outline: 0;
  -webkit-appearance: none;
}

input:invalid,
textarea:invalid {
  box-shadow: none;
}

@keyframes text-rotate {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(-360deg);
  }
}

/*
::-webkit-scrollbar {
	width: 10px;
	height: 10px;
}

::-webkit-scrollbar-button {
	display: none;
}

::-webkit-scrollbar-track {
}

::-webkit-scrollbar-track-piece {
	background-color: $color-grey6;
}

::-webkit-scrollbar-thumb {
	background-color: $color-orange;
	border-radius: none;
}

::-webkit-scrollbar-corner {
	background-color: #999;
}

::-webkit-resizer {
	background-color: #666;
}
*/
.label-subs {
  position: relative;
}

.label-subs > button[type="submit"] {
  width: auto;
  padding: 0;
  position: absolute;
  left: 50%;
  bottom: -30px;
  transform: translateX(-50%);
  font-size: 16px;
  font-weight: 600;
  letter-spacing: 1.2px;
  text-transform: uppercase;
  cursor: pointer;
  border: none;
  background-color: #ffffff;
  transition: color 0.3s ease;
}

@media (min-width: 576px) {
  .label-subs > button[type="submit"] {
    left: unset;
    bottom: unset;
    right: 0;
    top: 17px;
    transform: none;
  }
}

@media (min-width: 992px) {
  .label-subs > button[type="submit"]:hover {
    color: #efb834;
  }
}

.label-subs label {
  width: 100%;
  display: block;
  position: relative;
  padding: 15px 25px 15px 45px;
  border-bottom: 1px solid #000000;
  box-sizing: border-box;
}

@media (min-width: 576px) {
  .label-subs label {
    padding: 15px 100px 15px 45px;
  }
}

.label-subs label::before {
  content: "";
  width: 24px;
  height: 24px;
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 64 64'%3e%3cpath d='M58.7 10.7H5.3C2.4 10.7 0 13.1 0 16v32c0 2.9 2.4 5.3 5.3 5.3h53.3c2.9 0 5.3-2.4 5.3-5.3V16c.1-2.9-2.3-5.3-5.2-5.3zm0 35.7L40.5 29.3l18.1-10.9v28zM28.3 32.3c1.1.8 2.4 1.1 3.7 1.1 1.3 0 2.7-.3 3.7-1.1L52.5 48H10.7l17.1-16 .5.3zm24-16.3L33.1 27.7c-.5.3-1.3.3-1.9 0L11.7 16h40.6zM23.2 29.1L5.3 45.9V18.4l17.9 10.7z'/%3e%3c/svg%3e ");
  background-repeat: no-repeat;
  background-position: 50% 50%;
  background-size: 24px auto;
}

.label-subs label input {
  width: 100%;
  padding: 0;
}

.label-subs label input::placeholder {
  opacity: 0.5;
  color: #000000;
}

.label-subs label input:focus::placeholder {
  opacity: 0;
}

.custom-input {
  width: 100%;
  height: 46px;
  padding: 16px 20px;
  border: 1px solid #d4d4d4;
  background-color: #ffffff;
  box-sizing: border-box;
  transition: border-color 0.3s ease;
}

.custom-input::placeholder {
  font-size: 16px;
  opacity: 0.5;
}

.custom-input:focus {
  border-color: #000000;
}

.custom-input:focus::placeholder {
  opacity: 0;
}

input[type="search"] {
  border-bottom: 1px solid #000000;
  padding: 10px 0;
  background: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 64 64'%3e%3cpath d='M62.9 56.5l-17-13.8c7.2-9.9 6.1-23.7-2.7-32.5C38.4 5.3 32 2.7 25.3 2.7s-13 2.6-17.8 7.4S0 21.3 0 28s2.7 13.1 7.5 17.9c5.1 5.1 11.5 7.5 17.9 7.5 6.1 0 12.3-2.1 17.1-6.7l17.3 14.1c.5.5 1.1.5 1.6.5.8 0 1.6-.3 2.1-1.1.8-1 .8-2.6-.6-3.7zM25.3 48c-5.3 0-10.4-2.1-14.1-5.9-3.7-3.7-5.9-8.8-5.9-14.1s2.1-10.4 5.9-14.1S20 8 25.3 8s10.4 2.1 14.1 5.9 5.9 8.8 5.9 14.1-2.1 10.4-5.9 14.1c-3.7 3.8-8.7 5.9-14.1 5.9z'/%3e%3c/svg%3e ") no-repeat right center;
  background-size: 20px;
}

input[type="search"]::placeholder {
  font-size: 16px;
  opacity: 0.5;
}

input[type="search"]:focus::placeholder {
  opacity: 0;
}

textarea.custom-input {
  height: 150px;
  resize: none;
}

.slide-counter {
  font-size: 24px;
  line-height: 24px;
  color: #cccccc;
}

@media (min-width: 992px) {
  .slide-counter {
    font-size: 30px;
    line-height: 30px;
  }
}

.arrow-square {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: #ffffff;
  border: 1px solid #ffffff;
  position: absolute;
  z-index: 1;
  outline: none;
  padding: 0;
  transition: all 0.3s ease;
}

@media (min-width: 1200px) {
  .arrow-square {
    width: 60px;
    height: 60px;
  }
}

@media (min-width: 992px) {
  .arrow-square:hover {
    background-color: transparent;
  }
  .arrow-square:hover svg {
    fill: #ffffff;
  }
}

.arrow-square svg {
  width: 50%;
  height: 50%;
  fill: #000000;
  transition: fill 0.3s ease;
}

.arrow-square--prev svg {
  transform: rotate(180deg);
}

#pp-nav {
  display: none;
  right: 50px !important;
}

@media (min-width: 1560px) {
  #pp-nav {
    display: block;
  }
}

#pp-nav.dark ul li a span {
  background-color: #000000;
}

#pp-nav ul li {
  width: 80px;
  margin: 0;
}

#pp-nav ul li:hover a.active span {
  height: 1px !important;
  border-radius: 0 !important;
}

#pp-nav ul li:hover a span {
  margin: 0;
  background-color: #efb834;
}

#pp-nav ul li + li {
  margin-top: 27px;
}

#pp-nav ul li a span {
  width: 30px;
  height: 1px;
  border-radius: 0;
  clip: unset;
  background-color: #ffffff;
  left: unset;
  right: 0;
  border: none;
  transition: background-color 0.3s ease;
}

#pp-nav ul li a.active span {
  margin: 0;
  width: 80px;
  background-color: #f5480c;
  transition: width 0.3s ease;
}

/* 1.2  Typography */
@font-face {
  font-family: "Jost";
  src: url("../fonts/Jost-Light.woff2") format("woff2"), url("../fonts/Jost-Light.woff") format("woff");
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Jost";
  src: url("../fonts/Jost-Regular.woff2") format("woff2"), url("../fonts/Jost-Regular.woff") format("woff");
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Jost";
  src: url("../fonts/Jost-Italic.woff2") format("woff2"), url("../fonts/Jost-Italic.woff") format("woff");
  font-weight: 400;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: "Jost";
  src: url("../fonts/Jost-SemiBold.woff2") format("woff2"), url("../fonts/Jost-SemiBold.woff") format("woff");
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Jost";
  src: url("../fonts/Jost-Bold.woff2") format("woff2"), url("../fonts/Jost-Bold.woff") format("woff");
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Cinzel";
  src: url("../fonts/Cinzel-Regular.woff2") format("woff2"), url("../fonts/Cinzel-Regular.woff") format("woff");
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "EB Garamond";
  src: url("../fonts/Ebgaramondregular.woff2") format("woff2"), url("../fonts/Ebgaramondregular.woff") format("woff");
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

/* 1.3 Utils */
.visually-hidden {
  position: absolute;
  overflow: hidden;
  clip: rect(0 0 0 0);
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  border: 0;
}

.d-none {
  display: none !important;
}

.overflow-hidden {
  overflow: hidden;
}

.d-flex {
  display: flex;
}

@media (min-width: 992px) {
  .lg-d-flex {
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
}

@media (max-width: 991px) {
  .bg-dark {
    background-color: rgba(0, 0, 0, 0.6) !important;
  }
}

@media (max-width: 991px) {
  .bg-light {
    background-color: rgba(255, 255, 255, 0.6) !important;
  }
}

.field-error {
  font-size: 16px;
  line-height: 20px;
  color: #f5480c;
  margin-bottom: 15px;
  margin-top: 5px;
}

.grid-wrapper {
  display: flex;
  flex-wrap: wrap;
}

@media (min-width: 768px) {
  .grid-wrapper {
    margin: 0 -10px;
  }
}

@media (min-width: 1200px) {
  .grid-wrapper {
    margin: 0 -25px;
  }
}

/* 1.4 Buttons */
.btn {
  display: inline-block;
  vertical-align: top;
  padding: 16px 20px;
  font-size: 16px;
  line-height: 1;
  color: #ffffff;
  letter-spacing: 0.05em;
  text-transform: uppercase;
  text-align: center;
  border-radius: 5px;
  background-color: #efb834;
  cursor: pointer;
  transition: background-color 0.3s ease;
  /*&--play {
    display: flex;
    width: 50px;
    height: 50px;
    padding: 0;
    border-radius: 50%;
    justify-content: center;

    @include media(xl) {
      width: 58px;
      height: 58px;
    }

    svg {
      width: 16px;
      height: 16px;
      align-self: center;
      fill: $color-default-white;

      @include media(xl) {
        width: 20px;
        height: 20px;
      }
    }
  }*/
}

@media (min-width: 1200px) {
  .btn {
    padding: 20px 32px;
  }
}

.btn:hover, .btn:focus {
  outline: none;
  background-color: #ff6d33;
  color: #ffffff;
  box-shadow: none;
}

.btn:active {
  opacity: 0.7;
  box-shadow: none;
}

.btn--outline {
  background-color: transparent;
  color: #efb834;
  border-color: #efb834;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.btn--outline:hover, .btn--outline:focus {
  outline: none;
  background-color: #efb834;
  color: #ffffff;
}

.btn--technical {
  color: #000000;
  font-weight: 600;
  border: solid 2px #000000;
  border-radius: 0;
  background-color: transparent;
}

.btn--technical:hover, .btn--technical:focus {
  border-color: #ffdf91;
  background-color: #ffdf91;
}

.webpage--beige .btn {
  color: #000000;
  border-color: #e6da89;
  background-color: #e6da89;
}

.webpage--beige .btn:hover, .webpage--beige .btn:focus {
  border-color: #ddcd5f;
  background-color: #ddcd5f;
}

.webpage--modern .btn {
  background-color: #57695a;
}

.webpage--modern .btn:hover, .webpage--modern .btn:focus {
  background-color: #efb834;
}

.webpage--modern .btn--outline {
  background-color: transparent;
  color: #57695a;
  border-color: #57695a;
}

.webpage--modern .btn--outline:hover, .webpage--modern .btn--outline:focus {
  color: #ffffff;
  background-color: #57695a;
}

/* 1.5 Fields */
.field {
  position: relative;
  display: block;
  width: 100%;
  cursor: text;
}

.field .underline {
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
}

.field .underline::before, .field .underline::after {
  content: "";
  display: inline-block;
  position: absolute;
  bottom: 0;
  width: 0;
  height: 1px;
  background-color: #000000;
  transition: width 0.3s ease;
}

.field .underline::before {
  left: 50%;
}

.field .underline::after {
  right: 50%;
}

.field__hint {
  position: absolute;
  top: -1.7em;
  display: block;
  transition: 0.3s;
  font-size: 12px;
  text-transform: uppercase;
  color: #999999;
}

.field__hint .red {
  display: inline;
  position: relative;
  top: -7px;
  color: #ff0000;
}

.field input,
.field textarea {
  width: 100%;
  padding-bottom: 10px;
  border-bottom: 1px solid #cccccc;
  font-size: 20px;
}

.field input::placeholder,
.field textarea::placeholder {
  color: transparent;
}

.field input:placeholder-shown ~ .field__hint,
.field textarea:placeholder-shown ~ .field__hint {
  font-size: 20px;
  text-transform: none;
  top: -5px;
}

.field input:focus ~ .field__hint,
.field textarea:focus ~ .field__hint {
  position: absolute;
  top: -1.7em;
  display: block;
  font-size: 12px;
  text-transform: uppercase;
}

.field input:focus ~ .underline::before,
.field textarea:focus ~ .underline::before {
  width: 50%;
}

.field input:focus ~ .underline::after,
.field textarea:focus ~ .underline::after {
  width: 50%;
}

.field textarea {
  resize: none;
  height: 150px;
}

@media (min-width: 1200px) {
  .field textarea {
    height: 209px;
  }
}

@media screen and (min-width: 768px) and (max-width: 1199.98px) {
  .field--md-small .field__hint {
    top: -1.2em;
    font-size: 10px;
  }
  .field--md-small input,
  .field--md-small textarea {
    padding-bottom: 6px;
    font-size: 14px;
  }
  .field--md-small input:placeholder-shown ~ .field__hint,
  .field--md-small textarea:placeholder-shown ~ .field__hint {
    font-size: 14px;
  }
  .field--md-small input:focus,
  .field--md-small textarea:focus {
    border-bottom-color: #000000;
  }
  .field--md-small input:focus ~ .field__hint,
  .field--md-small textarea:focus ~ .field__hint {
    top: -1.2em;
    font-size: 10px;
  }
}

.field--bordered input,
.field--bordered textarea {
  padding: 16px 20px;
  font-size: 16px;
  line-height: 1;
  color: #000000;
  border: solid 1px #dadada;
  transition: border-color 0.3s ease;
}

@media (min-width: 1200px) {
  .field--bordered input,
  .field--bordered textarea {
    padding: 18px 30px;
  }
}

.field--bordered input:hover, .field--bordered input:focus,
.field--bordered textarea:hover,
.field--bordered textarea:focus {
  outline: none;
  border-color: #000000;
}

.field--bordered input::placeholder,
.field--bordered textarea::placeholder {
  color: #999999;
}

@media (min-width: 1200px) {
  .field--bordered textarea {
    height: 180px;
  }
}

/* 1.6 Video button */
.video-btn {
  display: flex;
  align-self: flex-start;
  font-size: 14px;
  line-height: 1.375;
  color: #000000;
  letter-spacing: 0.05em;
  text-transform: uppercase;
}

@media (min-width: 1200px) {
  .video-btn {
    font-size: 16px;
  }
}

@media (min-width: 1200px) {
  .video-btn--size-large .video-btn__btn {
    width: 90px;
    height: 90px;
  }
}

@media (min-width: 1200px) and (min-width: 1200px) {
  .video-btn--size-large .video-btn__btn svg {
    width: 30px;
    height: 30px;
  }
}

@media (min-width: 1200px) {
  .video-btn--size-large .video-btn__text {
    max-width: 100px;
    margin-left: 24px;
    font-size: 18px;
  }
}

.video-btn--outline .video-btn__btn {
  background-color: transparent;
  border: solid 1px #efb834;
}

.video-btn--outline .video-btn__btn svg {
  fill: #efb834;
  transition: fill 0.3s ease;
}

.video-btn--outline .video-btn__btn:hover, .video-btn--outline .video-btn__btn:focus {
  outline: none;
  background-color: #efb834;
}

.video-btn--outline .video-btn__btn:hover svg, .video-btn--outline .video-btn__btn:focus svg {
  fill: #ffffff;
}

.video-btn--technical .video-btn__btn {
  width: 52px;
  height: 52px;
  border: solid 2px #000000;
  background-color: transparent;
}

.video-btn--technical .video-btn__btn svg {
  fill: #000000;
}

.video-btn--technical .video-btn__btn:hover, .video-btn--technical .video-btn__btn:focus {
  background-color: #ffdf91;
}

@media (min-width: 1200px) {
  .video-btn--technical .video-btn__btn {
    width: 62px;
    height: 62px;
  }
}

.video-btn__btn {
  display: flex;
  width: 50px;
  height: 50px;
  padding: 0;
  border-radius: 50%;
  justify-content: center;
  background-color: #efb834;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.video-btn__btn:hover, .video-btn__btn:focus {
  outline: none;
  background-color: #ff6d33;
}

.video-btn__btn:active {
  opacity: 0.7;
}

@media (min-width: 1200px) {
  .video-btn__btn {
    width: 58px;
    height: 58px;
  }
}

.video-btn__btn svg {
  width: 16px;
  height: 16px;
  margin-left: 8.62%;
  align-self: center;
  fill: #ffffff;
}

@media (min-width: 1200px) {
  .video-btn__btn svg {
    width: 20px;
    height: 20px;
  }
}

.video-btn__text {
  max-width: 72px;
  margin-left: 10px;
  align-self: center;
}

@media (min-width: 1200px) {
  .video-btn__text {
    max-width: 100px;
    margin-left: 13px;
  }
}

.webpage--beige .video-btn {
  color: #ffffff;
}

.webpage--beige .video-btn__btn {
  background-color: #e6da89;
  border-color: #e6da89;
}

.webpage--beige .video-btn__btn:hover, .webpage--beige .video-btn__btn:focus {
  background-color: #ddcd5f;
}

.webpage--beige .video-btn__btn svg {
  fill: #ffffff;
}

.webpage--beige .video-btn--outline .video-btn__btn {
  background-color: transparent;
  border: solid 1px #e6da89;
}

.webpage--beige .video-btn--outline .video-btn__btn svg {
  fill: #e6da89;
}

.webpage--beige .video-btn--outline .video-btn__btn:hover, .webpage--beige .video-btn--outline .video-btn__btn:focus {
  background-color: #e6da89;
}

.webpage--beige .video-btn--outline .video-btn__btn:hover svg, .webpage--beige .video-btn--outline .video-btn__btn:focus svg {
  fill: #ffffff;
}

.webpage--modern .video-btn {
  font-size: 14px;
  line-height: 1.375;
  color: #57695a;
}

.webpage--modern .video-btn__btn {
  background-color: #57695a;
}

.webpage--modern .video-btn__btn:hover, .webpage--modern .video-btn__btn:focus {
  background-color: #edff2f;
}

.webpage--modern .video-btn--outline .video-btn__btn {
  background-color: transparent;
  border: solid 1px #57695a;
}

.webpage--modern .video-btn--outline .video-btn__btn svg {
  fill: #57695a;
}

.webpage--modern .video-btn--outline .video-btn__btn:hover, .webpage--modern .video-btn--outline .video-btn__btn:focus {
  background-color: #57695a;
}

.webpage--modern .video-btn--outline .video-btn__btn:hover svg, .webpage--modern .video-btn--outline .video-btn__btn:focus svg {
  fill: #ffffff;
}

/* 1.7 Heading */
.heading {
  margin: 0;
  font-size: 32px;
  line-height: 1.18;
  color: #000000;
  font-weight: 300;
  letter-spacing: -0.025em;
}

.heading--h1 {
  font-size: 36px;
}

.heading--upper {
  text-transform: uppercase;
}

@media (min-width: 576px) {
  .heading {
    font-size: 42px;
  }
  .heading--h1 {
    font-size: 48px;
  }
}

@media (min-width: 1200px) {
  .heading {
    font-size: 54px;
  }
  .heading--h1 {
    font-size: 64px;
  }
}

@media (min-width: 1200px) {
  .webpage--technical-drawing .heading {
    font-size: 60px;
  }
}

/* 1.8 Menu toggle button */
.menu-toggle {
  position: relative;
  display: block;
  width: 25px;
  height: 12px;
  padding: 0;
  border: none;
  background-color: transparent;
  appearance: none;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.menu-toggle--white::before, .menu-toggle--white::after {
  border-top: solid 3px #57695a;
}

.menu-toggle--opened::before {
  transform: translateY(2px);
}

.menu-toggle--opened::after {
  transform: translateY(-2px);
}

.menu-toggle::before, .menu-toggle::after {
  content: "";
  position: absolute;
  left: 0;
  width: 100%;
  border-top: solid 3px #2d3c2d;
  transition: border-top-color 0.3s ease, transform 0.3s ease;
}

.menu-toggle::before {
  top: 0;
}

.menu-toggle::after {
  bottom: 0;
}

.menu-toggle--white::before, .menu-toggle--white::after {
  border-top: solid 3px #57695a;
}

.menu-toggle:hover, .menu-toggle:focus {
  outline: none;
}

.menu-toggle:hover::before, .menu-toggle:hover::after, .menu-toggle:focus::before, .menu-toggle:focus::after {
  border-top-color: #efb834;
}

.webpage--modern .menu-toggle:hover::before, .webpage--modern .menu-toggle:hover::after, .webpage--modern .menu-toggle:focus::before, .webpage--modern .menu-toggle:focus::after {
  border-top-color: #efb834 !important;
}

/* 1.9 Header search */
.header-search {
  position: relative;
}

.header-search--white .header-search__toggle-icon {
  fill: #ffffff;
}

.header-search--opened .header-search__toggle-icon--loupe {
  display: none;
}

.header-search--opened .header-search__toggle-icon--close {
  display: block;
}

.header-search--opened .header-search__wrapper {
  display: block;
}

.header-search__wrapper {
  display: none;
  position: absolute;
  top: 50%;
  right: 30px;
  transform: translateY(-50%);
  background-color: #ffffff;
  padding: 10px 0;
  overflow: hidden;
  width: calc(100vw - 105px);
}

@media (min-width: 768px) {
  .header-search__wrapper {
    width: 50vw;
  }
}

@media (min-width: 992px) {
  .header-search__wrapper {
    width: 25vw;
  }
}

@media (min-width: 1200px) {
  .header-search__wrapper {
    width: 21vw;
    max-width: 404px;
  }
}

.header-search form label {
  display: block;
  margin: 0;
}

.header-search form input {
  display: block;
  width: 100%;
  padding: 10px 16px;
  border: none;
  appearance: none;
  font-size: 14px;
  line-height: 1;
  color: #000000;
  background-image: none;
  background-color: rgba(0, 0, 0, 0.01);
}

.header-search form input::-ms-clear, .header-search form input::-ms-reveal {
  display: none;
  width: 0;
  height: 0;
}

.header-search form input::-webkit-search-decoration, .header-search form input::-webkit-search-cancel-button, .header-search form input::-webkit-search-results-button, .header-search form input::-webkit-search-results-decoration {
  display: none;
}

.header-search form input:focus {
  outline: none;
  background-color: rgba(0, 0, 0, 0.02);
}

.header-search form input::placeholder {
  color: #999999;
  opacity: 1;
}

.header-search__toggle {
  position: relative;
  display: block;
  width: 22px;
  height: 22px;
  padding: 0;
  border: none;
  background-color: transparent;
  appearance: none;
  cursor: pointer;
}

.header-search__toggle-icon {
  display: block;
  width: 100%;
  height: auto;
  transition: fill 0.3s ease;
}

.header-search__toggle-icon--close {
  display: none;
}

.header-search__toggle:hover, .header-search__toggle:focus {
  outline: none;
}

.header-search__toggle:hover svg, .header-search__toggle:focus svg {
  fill: #efb834;
}

.webpage--beige .header-search__wrapper {
  background-color: #4d524b;
}

.webpage--beige .header-search__toggle-icon {
  fill: #ffffff;
}

.webpage--beige .header-search__toggle:hover, .webpage--beige .header-search__toggle:focus {
  outline: none;
}

.webpage--beige .header-search__toggle:hover svg, .webpage--beige .header-search__toggle:focus svg {
  fill: #e6da89 !important;
}

.webpage--beige .header-search form input {
  color: #ffffff;
  background-color: #5a5f57;
}

.webpage--modern .header-search__toggle:hover svg, .webpage--modern .header-search__toggle:focus svg {
  fill: #57695a !important;
}

/* 1.10 Chat button */
.lets-chat {
  position: relative;
  display: flex;
}
.lets-chat::before {
  content: "";
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 100%;
  border-bottom: solid 1px #57695a;
  transition: border-color 0.3s ease;
}

.lets-chat:hover, .lets-chat:focus {
  outline: none;
}

.lets-chat:hover .lets-chat__text, .lets-chat:focus .lets-chat__text {
  color: #efb834;
}

.lets-chat:hover::before, .lets-chat:focus::before {
  border-color: #efb834;
}

.lets-chat:hover svg, .lets-chat:focus svg {
  fill: #efb834;
}

.lets-chat__text {
  display: block;
  align-self: center;
  font-size: 14px;
  line-height: 1.285;
  color: #57695a;
  font-weight: 600;
  text-transform: uppercase;
  transition: color 0.3s ease;
}

.lets-chat__icon {
  align-self: center;
}

.lets-chat__icon svg {
  transition: fill 0.3s ease;
}

@media (min-width: 576px) {
  .lets-chat__icon svg {
    display: block;
    width: auto;
    height: 14px;
  }
}

/* 1.11 Navigation */
.navigation--column {
  display: block;
  width: 100%;
  padding: 6px 0;
  overflow: auto;
}

.navigation--column .navigation__list {
  display: block;
  padding-right: 10px;
}

.navigation--column .navigation__dropdown {
  display: none;
  opacity: 1;
  pointer-events: auto;
}

.navigation--column .navigation__item {
  margin-right: 0 !important;
  margin-bottom: 12px;
}

@media (min-height: 900px) {
  .navigation--column .navigation__item {
    margin-bottom: 22px;
  }
}

.navigation--column .navigation__item::after {
  display: none;
}

.navigation--column .navigation__link {
  font-size: 18px;
  line-height: 1.2;
  color: #000000;
  text-transform: none;
}

.navigation--column .navigation__link:hover::after, .navigation--column .navigation__link:focus::after {
  display: none;
}

.navigation--column .navigation__link:hover + .navigation__dropdown, .navigation--column .navigation__link:focus + .navigation__dropdown {
  display: none;
  opacity: 1;
  pointer-events: auto;
}

.navigation--column .navigation__dropdown {
  position: static;
  transform: translateY(0);
  width: 100%;
  min-width: 0;
  padding: 0 0 0 16px;
  border: none;
  background-color: transparent;
}

.navigation--column .navigation__dropdown-wrapper {
  display: block;
}

.navigation--column .navigation__dropdown-column {
  width: 100%;
}

.navigation--column .navigation__dropdown-column-title {
  display: none;
}

.navigation--column .navigation__dropdown-column + * {
  width: 100% !important;
  padding-left: 0 !important;
}

.navigation--column .navigation__dropdown-item a {
  font-size: 14px;
  line-height: 1;
}

.navigation--technical .navigation__item::after {
  display: none;
}

.navigation--technical .navigation__item--current .navigation__link {
  color: #000000;
  font-weight: 600;
}

.navigation--technical .navigation__item--current .navigation__link::before {
  opacity: 1;
}

.navigation--technical .navigation__link {
  position: relative;
}

.navigation--technical .navigation__link::before {
  content: "";
  display: block;
  width: 100%;
  height: 50%;
  position: absolute;
  top: 50%;
  left: 0;
  z-index: -1;
  background-color: #ffdf91;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.navigation--technical .navigation__link:hover, .navigation--technical .navigation__link:focus {
  color: #000000;
}

.navigation--technical .navigation__link:hover::before, .navigation--technical .navigation__link:focus::before {
  opacity: 1;
}

.navigation--technical .navigation__dropdown-item a {
  position: relative;
}

.navigation--technical .navigation__dropdown-item a::before {
  content: "";
  display: block;
  width: 100%;
  height: 50%;
  position: absolute;
  top: 50%;
  left: 0;
  z-index: -1;
  background-color: #ffdf91;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
}

.navigation--technical .navigation__dropdown-item a:hover, .navigation--technical .navigation__dropdown-item a:focus {
  color: #000000;
}

.navigation--technical .navigation__dropdown-item a:hover::before, .navigation--technical .navigation__dropdown-item a:focus::before {
  opacity: 1;
}

.navigation--technical .navigation__dropdown-item--current a {
  color: #000000 !important;
}

.navigation--technical .navigation__dropdown-item--current a::before {
  opacity: 1;
}

.navigation__dropdown {
  position: absolute;
  z-index: 1;
  min-width: 400px;
  background-color: #ffffff;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
}

.navigation__dropdown--columns {
  width: 670px;
  min-width: 0;
}

@media (min-width: 992px) {
  .navigation__dropdown--columns {
    width: 480px;
  }
}

@media (min-width: 1200px) {
  .navigation__dropdown--columns {
    width: 670px;
  }
}

.navigation__dropdown:hover {
  opacity: 1;
  pointer-events: auto;
}

@media (min-width: 992px) {
  .navigation__dropdown {
    left: -50px;
    bottom: -55px;
    transform: translateY(100%);
    padding: 42px 50px;
  }
}

@media (min-width: 1200px) {
  .navigation__dropdown {
    left: -100px;
    padding: 76px 100px 70px;
  }
}

@media (min-width: 992px) {
  .navigation__dropdown-wrapper {
    display: flex;
  }
}

@media (min-width: 992px) {
  .navigation__dropdown-column {
    flex-grow: 1;
  }
  .navigation__dropdown-column + .navigation__dropdown-column {
    width: 50%;
    padding-left: 32px;
  }
}

.navigation__dropdown-column-title {
  margin-bottom: 20px;
  font-size: 12px;
  line-height: 1;
  color: #999999;
  text-transform: uppercase;
  letter-spacing: 0.01em;
}

.navigation__dropdown-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.navigation__dropdown-item--current a {
  color: #efb834 !important;
  font-weight: 600;
}

.navigation__dropdown-item a {
  font-size: 16px;
  line-height: 1.875;
  color: #000000;
  letter-spacing: 0.015em;
  transition: color 0.3s ease;
}

.navigation__dropdown-item a:hover, .navigation__dropdown-item a:focus {
  outline: none;
  color: #efb834;
}

.navigation__list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-wrap: wrap;
}

.navigation__item {
  position: relative;
  margin-right: 20px;
}

@media (min-width: 992px) {
  .navigation__item {
    margin-right: 40px;
  }
}

@media (min-width: 1200px) {
  .navigation__item {
    margin-right: 60px;
  }
}

.navigation__item::after {
  content: "";
  position: absolute;
  top: 55%;
  right: -8px;
  transform: translateY(-50%) translateX(100%);
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: #cccccc;
}

@media (min-width: 992px) {
  .navigation__item::after {
    right: -17px;
    width: 6px;
    height: 6px;
  }
}

@media (min-width: 1200px) {
  .navigation__item::after {
    right: -27px;
  }
}

.navigation__item:last-child {
  margin-right: 0;
}

.navigation__item:last-child::after {
  display: none;
}

.navigation__item--current .navigation__link {
  color: #efb834;
  font-weight: 600;
}

.navigation__link {
  font-size: 12px;
  line-height: 1;
  color: #000000;
  text-transform: uppercase;
  transition: color 0.3s ease;
}

@media (min-width: 992px) {
  .navigation__link {
    font-size: 14px;
  }
}

.navigation__link:hover, .navigation__link:focus {
  outline: none;
  color: #efb834;
  position: relative;
}

.navigation__link:hover::after, .navigation__link:focus::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  transform: translateY(100%);
  width: 100%;
  height: 70px;
}

.navigation__link:hover + .navigation__dropdown, .navigation__link:focus + .navigation__dropdown {
  opacity: 1;
  pointer-events: auto;
}

.navigation__link:active {
  opacity: 0.7;
}

.webpage--beige .navigation__item::after {
  display: none;
}

.webpage--beige .navigation__dropdown {
  background-color: #3b3f39;
}

.webpage--beige .navigation__dropdown-item a {
  color: #ffffff;
}

.webpage--beige .navigation__dropdown-item a:hover, .webpage--beige .navigation__dropdown-item a:focus {
  color: #e6da89;
}

.webpage--beige .navigation__dropdown-item--current a {
  color: #e6da89 !important;
}

.webpage--modern .navigation__link:hover, .webpage--modern .navigation__link:focus {
  color: #efb834 !important;
}

.webpage--modern .navigation__item::after {
  display: none;
}

.webpage--modern .navigation__item--current .navigation__link {
  color: #efb834 !important;
}

.webpage--modern .navigation__dropdown-item a:hover, .webpage--modern .navigation__dropdown-item a:focus {
  color: #efb834;
}

.webpage--modern .navigation__dropdown-item--current a {
  color: #efb834 !important;
}

/* 1.12 Logo */
.logo {
  display: flex;
}

.logo__image {
  display: block;
  width: 42px;
  margin-right: 8px;
  align-self: center;
}

@media (min-width: 768px) {
  .logo__image {
    width: 52px;
    margin-right: 14px;
  }
}

@media (min-width: 992px) {
  .logo__image {
    margin-left: 2.41vw;
  }
}

@media (max-width: 991px) {
  .header__logo .logo__image img {
    height: 39.2px;
  }
}

.logo__image svg {
  display: block;
  width: 100%;
  height: auto;
  transition: fill 0.3s ease;
}

.logo__text {
  display: block;
  max-width: 110px;
  align-self: center;
  font-size: 12px;
  line-height: 1.285;
  color: #000000;
  text-transform: uppercase;
  font-weight: 600;
  letter-spacing: 0.22px;
  transition: color 0.3s ease;
}

@media (min-width: 768px) {
  .logo__text {
    font-size: 14px;
  }
}

.logo[href]:hover, .logo[href]:focus {
  outline: none;
}

.logo[href]:hover svg, .logo[href]:focus svg {
  fill: #efb834;
}

.logo[href]:hover .logo__text, .logo[href]:focus .logo__text {
  color: #efb834;
}

.webpage--technical-drawing .logo__image {
  display: block;
  width: 42px;
  margin-right: 8px;
  align-self: center;
}

@media (min-width: 768px) {
  .webpage--technical-drawing .logo__image {
    width: 45px;
    margin-right: 11px;
  }
}

@media (min-width: 768px) {
  .webpage--technical-drawing .logo__text {
    font-size: 12px;
  }
}

.webpage--technical-drawing .logo[href]:hover, .webpage--technical-drawing .logo[href]:focus {
  outline: none;
}

.webpage--technical-drawing .logo[href]:hover svg, .webpage--technical-drawing .logo[href]:focus svg {
  fill: #ffdf91;
}

.webpage--technical-drawing .logo[href]:hover .logo__text, .webpage--technical-drawing .logo[href]:focus .logo__text {
  color: #ffdf91;
}

.webpage--modern .logo__image svg {
  fill: #57695a;
}

.webpage--modern .logo__text {
  display: block;
  max-width: 110px;
  align-self: center;
  font-size: 12px;
  line-height: 1.285;
  color: #000000;
  text-transform: uppercase;
  font-weight: 600;
  letter-spacing: 0.22px;
  transition: color 0.3s ease;
}

@media (min-width: 768px) {
  .webpage--modern .logo__text {
    font-size: 14px;
  }
}

.webpage--modern .logo[href]:hover, .webpage--modern .logo[href]:focus {
  outline: none;
}

.webpage--modern .logo[href]:hover svg, .webpage--modern .logo[href]:focus svg {
  fill: #edff2f;
}

.webpage--modern .logo[href]:hover__text, .webpage--modern .logo[href]:focus__text {
  color: #57695a !important;
}

/* 1.13 Social */
.social {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-wrap: wrap;
}

.social--black .social__link {
  width: 20px;
  height: 20px;
  border: none;
}

.social--black .social__link svg {
  fill: #000000;
}

.social--black .social__link:hover, .social--black .social__link:focus {
  background-color: transparent;
}

.social--black .social__link:hover svg, .social--black .social__link:focus svg {
  fill: #efb834;
}

.social--technical .social__link {
  border-color: #000000;
}

.social--technical .social__link svg {
  fill: #000000;
}

.social--technical .social__link:hover, .social--technical .social__link:focus {
  border-color: #000000;
  background-color: #ffdf91;
}

.social--technical .social__link:hover svg, .social--technical .social__link:focus svg {
  fill: #000000;
}

.social__item + .social__item {
  margin-left: 16px;
}

@media (min-width: 576px) {
  .social__item + .social__item {
    margin-left: 24px;
  }
}

.social__link {
  display: flex;
  width: 40px;
  height: 40px;
  justify-content: center;
  border: solid 1px #fefae9;
  border-radius: 50%;
  transition: border-color 0.3s ease, background-color 0.3s ease;
}

.social__link svg {
  display: block;
  fill: #fefae9;
  align-self: center;
  transition: fill 0.3s ease;
}

.social__link:hover, .social__link:focus {
  outline: none;
  background-color: #efb834;
  border-color: #efb834;
}

.social__link:hover svg, .social__link:focus svg {
  fill: #ffffff;
}

.social__link:active {
  opacity: 0.7;
}

.social .fb {
  background-color: #3b5999;
  border-color: #3b5999;
  transition: all 0.3s ease;
}

@media (min-width: 992px) {
  .social .fb:hover svg {
    fill: #3b5999;
  }
}

.social .tw {
  background-color: #55acee;
  border-color: #55acee;
  transition: all 0.3s ease;
}

@media (min-width: 992px) {
  .social .tw:hover svg {
    fill: #55acee;
  }
}

.social .g-plus {
  background-color: #dd4b39;
  border-color: #dd4b39;
  transition: all 0.3s ease;
}

@media (min-width: 992px) {
  .social .g-plus:hover svg {
    fill: #dd4b39;
  }
}

.social .tumblr {
  background-color: #2b4b6a;
  border-color: #2b4b6a;
  transition: all 0.3s ease;
}

@media (min-width: 992px) {
  .social .tumblr:hover svg {
    fill: #2b4b6a;
  }
}

.social .rss {
  background-color: #fb7000;
  border-color: #fb7000;
  transition: all 0.3s ease;
}

@media (min-width: 992px) {
  .social .rss:hover svg {
    fill: #fb7000;
  }
}

.webpage--beige .social__link {
  border-color: #6a7067;
}

.webpage--beige .social__link svg {
  fill: #fefefe;
}

.webpage--beige .social__link:hover, .webpage--beige .social__link:focus {
  border-color: #e6da89;
  background-color: transparent;
}

.webpage--beige .social__link:hover svg, .webpage--beige .social__link:focus svg {
  fill: #e6da89;
}

.webpage--modern .social--black .social__link:hover svg, .webpage--modern .social--black .social__link:focus svg {
  fill: #57695a;
}

/* 1.14 More */
.more {
  font-size: 14px;
  line-height: 1;
  color: #000000;
  letter-spacing: 0.06em;
  text-transform: uppercase;
  transition: color 0.3s ease;
}

.more:hover, .more:focus {
  outline: none;
  color: #efb834;
}

.more:active {
  opacity: 0.7;
}

/* 1.15 More button */
.more-btn {
  display: flex;
  justify-content: center;
  width: 130px;
  height: 130px;
  border-radius: 50%;
  border: solid 1px #cccccc;
  transition: border-color 0.3s ease;
}

.more-btn:hover, .more-btn:focus {
  outline: none;
  border-color: #efb834;
}

.more-btn:hover .more-btn__text, .more-btn:focus .more-btn__text {
  color: #efb834;
}

.more-btn__text {
  font-size: 16px;
  line-height: 1;
  color: #000000;
  letter-spacing: 0.05em;
  text-transform: uppercase;
  align-self: center;
  transition: color 0.3s ease;
}

/* 1.16 Pagination */
.pagination {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
}

@media (min-width: 768px) {
  .pagination {
    justify-content: flex-start;
  }
}

.pagination__links {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  order: -1;
  margin-bottom: 20px;
}

@media (min-width: 768px) {
  .pagination__links {
    width: auto;
    justify-content: flex-start;
    order: 0;
    margin-bottom: 0;
  }
}

.pagination__link {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  font-size: 16px;
  color: #000000;
}

@media (min-width: 768px) {
  .pagination__link {
    width: 45px;
    height: 45px;
  }
}

@media (min-width: 992px) {
  .pagination__link:hover {
    color: #efb834;
  }
}

.pagination__link + .pagination__link {
  margin-left: 10px;
}

@media (min-width: 768px) {
  .pagination__link + .pagination__link {
    margin-left: 17px;
  }
}

.pagination__link.active {
  color: #f5480c;
  border: 2px dashed #f5480c;
}

.pagination__btn {
  font-size: 14px;
  text-transform: uppercase;
  color: #999999;
}

@media (min-width: 992px) {
  .pagination__btn:hover {
    color: #efb834;
  }
}

.pagination__btn--prev {
  margin-right: 20px;
}

.pagination__btn--next {
  margin-left: 30px;
}

/* 1.17 Filter */
.filter--technical .filter__item {
  font-weight: 400;
  position: relative;
  z-index: 1;
}

.filter--technical .filter__item::before {
  content: "";
  display: block;
  width: 100%;
  height: 14px;
  position: absolute;
  bottom: -40%;
  left: 0;
  z-index: -1;
  background-color: #ffdf91;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.filter--technical .filter__item:hover, .filter--technical .filter__item:focus {
  color: #000000;
}

.filter--technical .filter__item:hover::before, .filter--technical .filter__item:focus::before {
  opacity: 1;
}

.filter--technical .filter__item--active {
  font-weight: 700;
  color: #000000;
}

.filter--technical .filter__item--active::before {
  opacity: 1;
}

.filter__item {
  margin-right: 20px;
  padding: 0;
  font-size: 14px;
  line-height: 1;
  color: #000000;
  font-weight: 600;
  letter-spacing: 0.05em;
  text-transform: uppercase;
  background-color: transparent;
  border: none;
  appearance: none;
  cursor: pointer;
  transition: color 0.3s ease;
}

@media (min-width: 1200px) {
  .filter__item {
    margin-right: 32px;
    font-size: 16px;
  }
}

.filter__item:last-child {
  margin-right: 0;
}

.filter__item:hover, .filter__item:focus {
  outline: none;
  color: #efb834;
}

.filter__item--active {
  color: #efb834;
}

/* 1.18 Sort */
.sort {
  display: flex;
  align-items: baseline;
}

.sort__hint {
  margin-bottom: 0;
  margin-right: 20px;
  font-size: 14px;
  line-height: 1;
  color: #999999;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  align-self: center;
}

@media (min-width: 1200px) {
  .sort__hint {
    font-size: 16px;
  }
}

.sort__select {
  align-self: center;
  padding: 0 24px 0 0;
  font-size: 14px;
  line-height: 1;
  color: #000000;
  text-align: right;
  border: none;
  appearance: none;
  background-size: 12px 12px;
  background-position: right 0 top 50%;
  background-repeat: no-repeat;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 64 64'%3e%3cpath d='M32 48.1c-1.3 0-2.4-.5-3.5-1.3L.8 20.7C-.3 19.6-.3 18 .8 17c1.1-1.1 2.7-1.1 3.7 0L32 42.8l27.5-26.1c1.1-1.1 2.7-1.1 3.7 0 1.1 1.1 1.1 2.7 0 3.7L35.5 46.5c-1.1 1.4-2.2 1.6-3.5 1.6z'/%3e%3c/svg%3e");
}

@media (min-width: 1200px) {
  .sort__select {
    font-size: 16px;
  }
}

.sort__select::-ms-expand {
  display: none;
}

/* 1.19 Paginate */
.paginate {
  overflow: hidden;
}

.paginate--simple {
  display: flex;
  justify-content: center;
}

.paginate--simple .paginate__item {
  padding: 0 15px;
}

@media (min-width: 1200px) {
  .paginate--simple .paginate__item {
    height: 16.104vw;
  }
}

.paginate--simple .paginate__item a {
  font-size: 42px;
  line-height: 1;
  color: #000000;
  transition: color 0.3s ease;
}

@media (min-width: 1200px) {
  .paginate--simple .paginate__item a {
    font-size: 90px;
  }
}

.paginate--simple .paginate__item a:hover, .paginate--simple .paginate__item a:focus {
  outline: none;
  color: #efb834;
}

.paginate--simple .paginate__item a:active {
  opacity: 0.7;
}

.paginate--arrows {
  display: flex;
  justify-content: center;
  height: 82px;
  border-bottom: solid 1px rgba(0, 0, 0, 0.1);
}

@media (min-width: 768px) {
  .paginate--arrows {
    padding: 0 5px;
  }
}

@media (min-width: 1200px) {
  .paginate--arrows {
    padding: 0 13px;
  }
}

@media (min-width: 1560px) {
  .paginate--arrows {
    padding: 0 51px;
  }
}

.paginate--arrows .paginate__item {
  height: auto;
  padding: 0 15px;
  align-self: center;
  flex-shrink: 0;
}

.paginate--arrows .paginate__item--all {
  flex-grow: 1;
  text-align: center;
}

.paginate--arrows .paginate__item a {
  display: inline-block;
  vertical-align: top;
}

.paginate--arrows .paginate__item a.disabled svg {
  fill: #999999;
}

.paginate--arrows .paginate__item a svg {
  display: block;
  transition: fill 0.3s ease;
}

.paginate--arrows .paginate__item a:not(.disabled):hover, .paginate--arrows .paginate__item a:not(.disabled):focus {
  outline: none;
}

.paginate--arrows .paginate__item a:not(.disabled):hover svg, .paginate--arrows .paginate__item a:not(.disabled):focus svg {
  fill: #efb834;
}

.paginate--arrows .paginate__item a:active {
  opacity: 0.7;
}

.paginate__item {
  position: relative;
  z-index: 0;
  height: 120px;
}

@media (min-width: 768px) {
  .paginate__item {
    height: 15.104vw;
  }
}

.paginate__item--prev .paginate__image {
  right: 0;
}

.paginate__item--next {
  text-align: right;
}

.paginate__item--next a {
  justify-content: flex-end;
}

.paginate__item--next .paginate__image {
  left: 0;
}

.paginate__item a {
  display: flex;
  height: 100%;
}

.paginate__item a:hover, .paginate__item a:focus {
  outline: none;
}

.paginate__item a:hover .paginate__image, .paginate__item a:focus .paginate__image {
  opacity: 1;
}

.paginate__item a:hover .paginate__text span, .paginate__item a:focus .paginate__text span {
  top: -30px;
  opacity: 0;
}

.paginate__item a:active {
  opacity: 0.7;
}

.paginate__image {
  position: absolute;
  top: 0;
  z-index: -1;
  width: 50vw;
  height: 100%;
  opacity: 0;
  transition: all 500ms 500ms ease-in-out;
}

.paginate__image img {
  display: block;
  width: auto;
  height: 100%;
}

.paginate__text {
  align-self: center;
  font-size: 20px;
  line-height: 1.3;
  color: #000000;
}

@media (min-width: 1200px) {
  .paginate__text {
    font-size: 30px;
  }
}

.paginate__text span.hint {
  position: relative;
  top: 0;
  display: block;
  margin-bottom: 8px;
  transition: all 400ms 200ms ease-in-out;
  font-size: 12px;
  line-height: 1;
  color: #999999;
  text-transform: uppercase;
  letter-spacing: 0.075em;
}

@media (min-width: 1200px) {
  .paginate__text span.hint {
    margin-bottom: 16px;
    font-size: 14px;
  }
}

.paginate__text span:not(.hint) {
  position: relative;
  top: 0;
  transition: all 400ms 300ms ease-in-out;
}

/* 1.20 Overlay */
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 999;
  cursor: pointer;
  background-color: rgba(0, 0, 0, 0.8);
  display: none;
}

/* 1.21 Modal */
.modal {
  position: fixed;
  top: 50%;
  left: 50%;
  z-index: 1000;
  transform: translateX(-50%) translateY(-50%);
  width: 640px;
  height: auto;
  padding: 40px;
  background-color: #ffffff;
  display: none;
}

.modal__close {
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  justify-content: center;
  width: 48px;
  height: 48px;
  padding: 0;
  border: none;
  background-color: transparent;
  appearance: none;
  cursor: pointer;
}

.modal__close svg {
  fill: #000000;
  align-self: center;
  transition: fill 0.3s ease;
}

.modal__close:hover, .modal__close:focus {
  outline: none;
}

.modal__close:hover svg, .modal__close:focus svg {
  fill: #efb834;
}

.modal__close:active {
  opacity: 0.7;
}

.modal--video {
  width: 90vw;
  padding: 0;
}

.modal--video .wrapper {
  position: relative;
  padding-bottom: 56.25%;
}

.modal--video .wrapper iframe {
  position: absolute;
  width: 100%;
  height: 100%;
  border: none;
}

@media (min-width: 768px) {
  .modal--video {
    width: 70vw;
  }
}

.modal--video video {
  display: block;
  width: 100%;
  height: auto;
  max-height: 100%;
  border: none;
}

.modal--video .modal__close {
  transform: translateY(-100%);
}

.modal--video .modal__close svg {
  fill: #ffffff;
}

.modal--video .modal__close:hover svg, .modal--video .modal__close:focus svg {
  fill: #efb834;
}

.popup--thanks .popup__title {
  font-size: 32px;
  text-align: center;
}

@media (min-width: 992px) {
  .popup--thanks .popup__title {
    font-size: 45px;
  }
}

.webpage--beige .modal__close:hover, .webpage--beige .modal__close:focus {
  outline: none;
}

.webpage--beige .modal__close:hover svg, .webpage--beige .modal__close:focus svg {
  fill: #e6da89 !important;
}

/* 1.22 Preloader */
.preloader {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
  display: flex;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: #ffffff;
}

.preloader__text {
  align-self: center;
  font-size: 48px;
}

.preloader__spinner {
  position: relative;
  width: 50px;
  height: 50px;
  align-self: center;
}

.preloader__double-bounce {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  animation: bounce 2s infinite ease-in-out;
  opacity: 0.6;
  border-radius: 50%;
  background-color: #efb834;
}

.preloader__double-bounce--delay {
  animation-delay: -1s;
}

.webpage--beige .preloader {
  background-color: #d8d5bf;
}

.webpage--beige .preloader__double-bounce {
  background-color: #4d524b;
}

.webpage--modern .preloader__double-bounce {
  background-color: #57695a;
}

@keyframes bounce {
  0%,
  100% {
    transform: scale(0);
  }
  50% {
    transform: scale(1);
  }
}

/* 1.23 Webpage */
.webpage--beige {
  background-color: #4d524b;
}

.webpage--beige main {
  background-color: #4d524b;
}

.webpage__an-awards {
  margin-bottom: 60px;
}

@media (min-width: 768px) {
  .webpage__an-awards {
    margin-bottom: 100px;
  }
}

@media (min-width: 1200px) {
  .webpage__an-awards {
    margin-bottom: 165px;
  }
}

.webpage__our-story {
  margin-bottom: 60px;
}

@media (min-width: 768px) {
  .webpage__our-story {
    margin-bottom: 100px;
  }
}

@media (min-width: 1200px) {
  .webpage__our-story {
    margin-bottom: 165px;
  }
}

.webpage__type-service {
  margin-bottom: 60px;
}

@media (min-width: 768px) {
  .webpage__type-service {
    margin-bottom: 100px;
  }
}

@media (min-width: 1200px) {
  .webpage__type-service {
    margin-bottom: 172px;
  }
}

.webpage__latest-projects {
  margin-bottom: 60px;
}

@media (min-width: 768px) {
  .webpage__latest-projects {
    margin-bottom: 100px;
  }
}

@media (min-width: 1200px) {
  .webpage__latest-projects {
    margin-bottom: 67px;
  }
}

.webpage__review {
  margin-bottom: 60px;
}

@media (min-width: 768px) {
  .webpage__review {
    margin-bottom: 100px;
  }
}

@media (min-width: 1200px) {
  .webpage__review {
    margin-bottom: 185px;
  }
}

.webpage__review + .webpage__partners {
  margin-bottom: 0;
  padding-bottom: 60px;
}

@media (min-width: 768px) {
  .webpage__review + .webpage__partners {
    margin-bottom: 0;
    padding-bottom: 100px;
  }
}

@media (min-width: 1200px) {
  .webpage__review + .webpage__partners {
    margin-bottom: 0;
    padding-bottom: 143px;
  }
}

.webpage__rewards {
  margin-bottom: 60px;
}

@media (min-width: 768px) {
  .webpage__rewards {
    margin-bottom: 100px;
  }
}

@media (min-width: 1200px) {
  .webpage__rewards {
    margin-bottom: 128px;
  }
}

.webpage__partners {
  padding: 112px 0;
  background-color: #2d3c2d;
}

.webpage__article-list {
  padding-bottom: 100px;
}

@media (min-width: 768px) {
  .webpage__article-list {
    padding-bottom: 150px;
  }
}

@media (min-width: 1200px) {
  .webpage__article-list {
    padding-bottom: 212px;
  }
}

.webpage__hero-banner {
  margin-bottom: 60px;
}

@media (min-width: 1200px) {
  .webpage__hero-banner {
    margin-bottom: 150px;
  }
}

.webpage__latest-articles {
  margin-bottom: 60px;
}

.webpage__instagram-block {
  padding-bottom: 60px;
}

@media (min-width: 1200px) {
  .webpage__instagram-block {
    padding-bottom: 196px;
  }
}

/* 1.24 Language switcher */
.lang-switcher {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-wrap: wrap;
}

.lang-switcher--white .lang-switcher__link {
  color: #ffffff;
}

.lang-switcher--upper .lang-switcher__link {
  text-transform: uppercase;
}

.lang-switcher--menu .lang-switcher__item {
  margin-right: 24px;
}

.lang-switcher--menu .lang-switcher__link {
  font-size: 14px;
  letter-spacing: 0.05em;
  color: #000000;
}

.lang-switcher--menu .lang-switcher__link--current {
  color: #efb834;
}

.lang-switcher__item {
  margin-right: 14px;
}

@media (min-width: 1200px) {
  .lang-switcher__item {
    margin-right: 28px;
  }
}

.lang-switcher__item:last-child {
  margin-right: 0;
}

.lang-switcher__link {
  font-size: 12px;
  line-height: 1;
  color: #999999;
  text-transform: uppercase;
  transition: color 0.3s ease;
}

@media (min-width: 1200px) {
  .lang-switcher__link {
    font-size: 14px;
  }
}

.lang-switcher__link--current {
  color: #000000;
  font-weight: 600;
  pointer-events: none;
}

.lang-switcher__link:hover, .lang-switcher__link:focus {
  outline: none;
  color: #efb834;
}

.lang-switcher__link:not(.lang-switcher__link--current):active {
  opacity: 0.7;
}

.webpage--modern .lang-switcher__link:hover, .webpage--modern .lang-switcher__link:focus {
  color: #57695a !important;
}

.webpage--modern .lang-switcher--menu .lang-switcher__link:hover, .webpage--modern .lang-switcher--menu .lang-switcher__link:focus {
  color: #57695a;
}

.webpage--modern .lang-switcher--menu .lang-switcher__link--current {
  color: #57695a;
}

.webpage--beige .lang-switcher__link:hover, .webpage--beige .lang-switcher__link:focus {
  color: #e6da89 !important;
}

.webpage--beige .lang-switcher--menu .lang-switcher__link {
  color: #ffffff;
}

.webpage--beige .lang-switcher--menu .lang-switcher__link:hover, .webpage--beige .lang-switcher--menu .lang-switcher__link:focus {
  color: #e6da89;
}

.webpage--beige .lang-switcher--menu .lang-switcher__link--current {
  color: #e6da89;
}

.webpage--technical-drawing .lang-switcher__link:hover, .webpage--technical-drawing .lang-switcher__link:focus {
  color: #ffdf91 !important;
}

.webpage--technical-drawing .lang-switcher--menu .lang-switcher__link:hover, .webpage--technical-drawing .lang-switcher--menu .lang-switcher__link:focus {
  color: #ffdf91;
}

.webpage--technical-drawing .lang-switcher--menu .lang-switcher__link--current {
  color: #ffdf91;
}

/*-------------------------------------------------------------------------------
  2. Header
-------------------------------------------------------------------------------*/
/* 2.1 Header type 1 */
.header {
  position: relative;
  z-index: 2;
  border-top: solid 5px #f5480c;
  background-color: #fefae9;
}

.header--aside {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 2;
  width: calc(100% - 52px);
  max-width: 375px;
  height: 100vh;
  min-height: 500px;
  padding: 0 !important;
  border-top: none;
  border-right: solid 1px #e2e2e2;
  background-color: #ffffff;
  transform: translateX(-100%);
  transition: transform 0.3s ease;
}

@media (min-width: 1200px) {
  .header--aside {
    transition: none;
    transform: translateX(0);
    max-width: 300px;
  }
}

@media (min-width: 1560px) {
  .header--aside {
    max-width: 375px;
  }
}

.header--aside .header__inner {
  padding: 6.48vh 20px 8vh !important;
  flex-direction: column;
  height: 100%;
}

@media (min-width: 576px) {
  .header--aside .header__inner {
    padding-left: 3.64vw !important;
    padding-right: 3.64vw !important;
  }
}

.header--aside .header__inner > * {
  align-self: flex-start;
}

.header--aside .header__inner::before {
  content: "";
  width: 100%;
  margin: 4vh 0 3vh;
  border-bottom: solid 1px #e2e2e2;
}

@media (min-height: 900px) {
  .header--aside .header__inner::before {
    margin: 6.48vh 0 3.56vh;
  }
}

.header--aside .header__logo {
  margin: 0;
  order: -1;
}

.header--aside .header__lang-switcher {
  margin-bottom: 4vh;
  margin-left: 0;
  margin-right: 0;
}

@media (min-height: 900px) {
  .header--aside .header__lang-switcher {
    margin-bottom: 5.5vh;
  }
}

.header--aside .header__social {
  margin-top: 30px;
  margin-bottom: 2.5vh;
}

@media (min-height: 900px) {
  .header--aside .header__social {
    margin-bottom: 4.44vh;
  }
}

.header--aside .header__nav {
  display: block;
  margin-bottom: auto;
}

.header--aside .header__toggle {
  position: absolute;
  top: 12px;
  right: 0;
  transform: translateX(100%);
}

@media (min-width: 1200px) {
  .header--aside .header__toggle {
    display: none;
  }
}

.header--aside .header__address {
  margin-bottom: 8px;
}

.header--opened {
  z-index: 1000;
  transform: translateX(0);
}

@media (min-width: 1200px) {
  .header--opened {
    z-index: 2;
  }
}

.header--white {
  border: none !important;
}

.header--white .header__inner {
  position: relative;
}

@media (min-width: 1200px) {
  .header--white .header__lang-switcher {
    margin-right: 5.41vw;
  }
}

.header--white .logo__text {
  color: #ffffff;
}

.header--white .logo[href]:hover, .header--white .logo[href]:focus {
  color: #efb834;
}

.header--white .lang-switcher__link {
  color: #ffffff;
}

.header--white .lang-switcher__link:hover, .header--white .lang-switcher__link:focus {
  color: #efb834;
}

.header--white .header-search__toggle svg {
  fill: #ffffff;
}

.header--white .header-search__toggle:hover svg, .header--white .header-search__toggle:focus svg {
  fill: #efb834;
}

.header--white .menu-toggle::before, .header--white .menu-toggle::after {
  border-top: solid 3px #57695a;
}

.header--white .menu-toggle:hover::before, .header--white .menu-toggle:hover::after, .header--white .menu-toggle:focus::before, .header--white .menu-toggle:focus::after {
  border-top-color: #efb834;
}

.header--white .navigation__link:hover, .header--white .navigation__link:focus {
  color: #efb834;
}

.header--white .navigation__item--current .header--white .navigation__link {
  color: #efb834 !important;
}

.header--absolute {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 2;
}

.header--intro {
  background-color: transparent;
}

.header--fixed {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 2;
  background-color: #fefae9;
  transform: translateY(99%);
  border-top: none;
  transition: transform 0.5s ease;
}

@media (min-width: 768px) {
  .header--fixed .header__inner {
    padding-top: 24px;
    padding-bottom: 24px;
  }
}

.header--fixed .logo__image {
  width: 36px;
  margin-right: 10px;
}
.header--fixed .logo__image img {
  height: 39.2px;
  margin-right: 10px;
}

.header--fixed .logo__text {
  font-size: 10px;
}

@media (min-width: 992px) {
  .header--fixed .header__logo {
    margin-right: calc(8.69vw + 20px);
  }
}

@media (min-width: 992px) {
  .header--fixed .navigation__dropdown {
    bottom: -29px;
  }
}

.header--technical {
  border-top: none;
  border-bottom: solid 1px #000000;
}

.header--technical .header__inner {
  max-width: 1920px;
  padding-top: 0;
  padding-bottom: 0;
}

.header--technical .header__logo {
  margin-right: 0;
}

.header--technical .lang-switcher__link:hover, .header--technical .lang-switcher__link:focus {
  color: #ffdf91;
}

.header--technical .header-search__toggle:hover svg, .header--technical .header-search__toggle:focus svg {
  fill: #ffdf91;
}

@media (min-width: 768px) {
  .header {
    border-top-width: 7px;
  }
}

@media (min-width: 992px) {
  .header {
    border-top-width: 10px;
  }
}

.header__purchase {
  margin-left: auto;
  border-radius: 0;
  align-self: center;
}

.header__purchase-text {
  display: none;
}

@media (min-width: 576px) {
  .header__purchase-text {
    display: inline;
  }
}

.header__purchase svg {
  display: block;
  fill: #ffffff;
}

@media (min-width: 576px) {
  .header__purchase svg {
    display: none;
  }
}

.header__inner {
  display: flex;
  width: 100%;
  max-width: 1550px;
  margin: 0 auto;
  padding: 15px 20px;
}

@media (min-width: 768px) {
  .header__inner {
    padding: 32px 15px;
  }
}

@media (min-width: 992px) {
  .header__inner {
    padding: 48px 15px 44px;
  }
}

.header__inner > * {
  align-self: center;
}

.header__lang-switcher {
  margin-right: 20px;
  margin-left: auto;
}

@media (min-width: 768px) {
  .header__lang-switcher {
    margin-right: 5.41vw;
  }
}

.header__logo {
  margin-right: 20px;
}

@media (min-width: 768px) {
  .header__logo {
    margin-right: 0;
  }
}

@media (min-width: 992px) {
  .header__logo {
    margin-right: 8.69vw;
    margin-top: -5px;
  }
}

.header__nav {
  display: none;
}

@media (min-width: 992px) {
  .header__nav {
    display: block;
  }
}

.header__chat {
  margin-right: 20px;
  margin-left: auto;
}

@media (min-width: 768px) {
  .header__chat {
    margin-right: 2.41vw;
  }
}

.header__search {
  margin: 0;
}

.header__menu-toggle {
  margin-left: 20px;
}

@media (min-width: 768px) {
  .header__menu-toggle {
    margin-left: 2.41vw;
  }
}

@media (min-width: 992px) {
  .header__menu-toggle {
    display: none;
    margin-left: 1.66vw;
  }
  .header__menu-toggle--always {
    display: block;
  }
}

.header__address {
  font-size: 18px;
  line-height: 1.44;
  color: #666666;
  font-style: normal;
}

.header__phone {
  font-size: 18px;
  line-height: 1.44;
  color: #666666;
  transition: color 0.3s ease;
}

.header__phone:hover, .header__phone:focus {
  outline: none;
  color: #efb834;
}

.header__phone:active {
  opacity: 0.7;
}

.header__part {
  display: flex;
  align-items: center;
  align-self: stretch;
  padding-top: 15px;
  padding-bottom: 15px;
}

@media (min-width: 992px) {
  .header__part--start {
    padding-left: 15px;
    padding-right: 4.16vw;
    border-right: solid 1px #000000;
  }
}

.header__part--center {
  flex-grow: 1;
}

@media (min-width: 992px) {
  .header__part--center {
    padding-left: 4.16vw;
  }
}

@media (min-width: 992px) {
  .header__part--end {
    padding-left: 2.91vw;
    padding-right: 15px;
    border-left: solid 1px #000000;
  }
}

.webpage--beige .header {
  background-color: #4d524b;
}

.webpage--beige .header--white .logo svg {
  fill: #e6da89;
}

.webpage--beige .header--white .logo[href]:hover svg,
.webpage--beige .header--white .logo[href]:focus svg {
  fill: #e6da89;
}

.webpage--beige .header--white .logo[href]:hover .logo__text,
.webpage--beige .header--white .logo[href]:focus .logo__text {
  color: #e6da89 !important;
}

.webpage--beige .header--white .lang-switcher__link {
  color: #ffffff;
}

.webpage--beige .header--white .lang-switcher__link:hover, .webpage--beige .header--white .lang-switcher__link:focus {
  color: #e6da89;
}

.webpage--beige .header--white .search-toggle svg {
  fill: #ffffff;
}

.webpage--beige .header--white .search-toggle:hover svg, .webpage--beige .header--white .search-toggle:focus svg {
  fill: #e6da89;
}

.webpage--beige .header--white .menu-toggle::before, .webpage--beige .header--white .menu-toggle::after {
  border-top: solid 3px #57695a;
}

.webpage--beige .header--white .menu-toggle:hover::before, .webpage--beige .header--white .menu-toggle:hover::after, .webpage--beige .header--white .menu-toggle:focus::before, .webpage--beige .header--white .menu-toggle:focus::after {
  border-top-color: #e6da89;
}

.webpage--beige .header--white .navigation__link {
  color: #ffffff;
}

.webpage--beige .header--white .navigation__link:hover, .webpage--beige .header--white .navigation__link:focus {
  color: #e6da89;
}

.webpage--beige .header--white .navigation__item--current .navigation__link {
  color: #e6da89 !important;
}

.webpage--beige .header--fixed {
  background-color: #4d524b;
}

.webpage--modern .header {
  border-top: none;
}

.webpage--modern .header--white {
  background-color: transparent;
}

.webpage--modern .header--white.header--fixed {
  background-color: #fefae9;
}

.webpage--modern .header--white.header--fixed .logo__text {
  color: #000000;
}

.webpage--modern .header--white.header--fixed .logo[href]:hover, .webpage--modern .header--white.header--fixed .logo[href]:focus {
  color: #57695a;
}

.webpage--modern .header--white.header--fixed .lang-switcher__link {
  color: #000000;
}

.webpage--modern .header--white.header--fixed .lang-switcher__link:hover, .webpage--modern .header--white.header--fixed .lang-switcher__link:focus {
  color: #57695a;
}

.webpage--modern .header--white.header--fixed .header-search__toggle svg {
  fill: #000000;
}

.webpage--modern .header--white.header--fixed .header-search__toggle:hover svg, .webpage--modern .header--white.header--fixed .header-search__toggle:focus svg {
  fill: #57695a;
}

.webpage--modern .header--white.header--fixed .menu-toggle::before, .webpage--modern .header--white.header--fixed .menu-toggle::after {
  border-top: solid 3px #2d3c2d;
}

.webpage--modern .header--white.header--fixed .menu-toggle:hover::before, .webpage--modern .header--white.header--fixed .menu-toggle:hover::after, .webpage--modern .header--white.header--fixed .menu-toggle:focus::before, .webpage--modern .header--white.header--fixed .menu-toggle:focus::after {
  border-top-color: #57695a;
}

.webpage--modern .header--white.header--fixed .navigation__link {
  color: #000000;
}

.webpage--modern .header--white.header--fixed .navigation__link:hover, .webpage--modern .header--white.header--fixed .navigation__link:focus {
  color: #efb834;
}

.webpage--modern .header--white.header--fixed .navigation__item--current .navigation__link {
  color: #efb834 !important;
}

/* 2.2 Header type 2 */
.header-3 {
  display: block;
  width: 100%;
  border-top: none;
  background-color: transparent;
  padding: 8px 0;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 2;
  transition: background-color 0.3s ease;
}

@media (min-width: 992px) {
  .header-3 {
    padding: 48px 0 15px;
  }
}

.header-3 .logo__image svg {
  fill: #ffffff;
  transition: fill 0.3s ease;
}

.header-3 .logo__text {
  color: #ffffff;
}

@media (min-width: 992px) {
  .header-3 .logo:hover .logo__image svg {
    fill: #efb834;
  }
}

.header-3 .header__inner {
  max-width: 100%;
  padding: 0 15px;
  justify-content: space-between;
}

@media (min-width: 992px) {
  .header-3 .header__inner {
    padding: 0 50px;
  }
}

.header-3 .header__menu-toggle-wrapper {
  display: flex;
  align-items: center;
  align-self: center;
}

@media (min-width: 992px) {
  .header-3 .header__menu-toggle-wrapper {
    align-self: flex-start;
  }
}

.header-3 .header__menu-toggle-wrapper span {
  font-size: 14px;
  line-height: 18px;
  text-transform: uppercase;
  color: #ffffff;
  padding-right: 17px;
  transition: color 0.3s ease;
}

.header-3 .header__menu-toggle::before, .header-3 .header__menu-toggle::after {
  border-top-color: #ffffff;
  transition: border-top-color 0.3s ease;
}

@media (min-width: 992px) {
  .header-3 .header__menu-toggle:hover::before, .header-3 .header__menu-toggle:hover::after {
    border-top-color: #efb834;
  }
}

.header-3--dark .logo__image svg {
  fill: #000000;
}

.header-3--dark .logo__text {
  color: #000000;
}

.header-3--dark .header__menu-toggle-wrapper span {
  color: #000000;
}

.header-3--dark .header__menu-toggle::before, .header-3--dark .header__menu-toggle::after {
  border-top-color: #000000;
}

/*-------------------------------------------------------------------------------
  3. Footer
-------------------------------------------------------------------------------*/
/* 3.1 Footer type 1 */
.footer {
  padding: 0 0 32px;
  background-color: #f9fbfd;
  background-repeat: no-repeat;
  background-position: 50% 0;
  background-image: url("../img/footer-bg.jpg");
  background-size: cover;
}

@media (min-width: 768px) {
  .footer {
    padding: 0 0 40px;
  }
}

@media (min-width: 992px) {
  .footer {
    padding: 0 0 40px;
  }
}

@media (min-width: 1200px) {
  .footer {
    padding: 0 0 80px;
  }
}

.footer--lite {
  background-color: #ffffff;
  background-image: none;
}

@media (min-width: 768px) {
  .footer--lite {
    padding: 60px 0;
  }
}

@media (min-width: 992px) {
  .footer--lite {
    padding: 80px 0;
  }
}

@media (min-width: 1200px) {
  .footer--lite {
    padding: 90px 0;
  }
}

.footer--lite .footer__menu {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  margin-top: -16px;
}

@media (min-width: 768px) {
  .footer--lite .footer__menu {
    justify-content: flex-end;
  }
}

.footer--lite .footer__menu-item {
  margin-right: 16px;
  margin-top: 16px;
}

.footer--lite .footer__menu-link {
  display: block;
  font-size: 14px;
  line-height: 1.28;
  color: #666666;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.footer--lite .footer__menu-link:hover, .footer--lite .footer__menu-link:focus {
  outline: none;
  color: #000000;
}

.footer--lite .footer__copyright {
  font-size: 16px;
  line-height: 1.25;
  color: #000000;
}

.footer--lite .footer__copyright .grey {
  color: #666666;
}

.footer--modern {
  background-image: none;
  background-color: #fefae9;
}

.footer--modern .footer__column-title {
  text-transform: uppercase;
  font-weight: 700;
}

@media (min-width: 576px) {
  .footer--modern .footer__inner {
    max-width: unset;
  }
}

@media (max-width: 768px) {
  .footer--modern .footer__inner {
    padding-right: 20px;
    padding-left: 20px;
  }
}

@media (min-width: 1200px) {
  .footer--modern .footer__inner {
    max-width: 1550px;
  }
}

.footer--technical {
  padding-top: 0;
  border-top: solid 4px #000000;
  background-image: none;
  background-color: transparent;
}

.footer--technical .footer__top {
  margin-bottom: 40px;
  padding-top: 40px;
  padding-bottom: 40px;
  border-bottom: solid 1px #000000;
  background-color: #f9fbfd;
  background-repeat: no-repeat;
  background-position: 50% 0;
  background-image: url("../img/footer-bg.jpg");
  background-size: 100% auto;
}

@media (min-width: 1200px) {
  .footer--technical .footer__top {
    margin-bottom: 135px;
    padding-top: 135px;
    padding-bottom: 135px;
  }
}

.footer--technical .footer__top p {
  color: #000000;
}

.footer--technical .footer__email {
  font-weight: 400;
  color: #000000;
  letter-spacing: normal;
}

@media (min-width: 1200px) {
  .footer--technical .footer__email {
    font-size: 90px;
  }
}

.footer--technical .footer__column-item--current .footer__column-link, .footer--technical .footer__column-link {
  position: relative;
}

.footer--technical .footer__column-item--current .footer__column-link::before, .footer--technical .footer__column-link::before {
  content: "";
  display: block;
  width: 100%;
  height: 14px;
  position: absolute;
  top: 55%;
  left: 0;
  z-index: -1;
  background-color: #ffdf91;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.footer--technical .footer__column-item--current .footer__column-link:hover, .footer--technical .footer__column-item--current .footer__column-link:focus, .footer--technical .footer__column-link:hover, .footer--technical .footer__column-link:focus {
  color: #000000;
}

.footer--technical .footer__column-item--current .footer__column-link:hover::before, .footer--technical .footer__column-item--current .footer__column-link:focus::before, .footer--technical .footer__column-link:hover::before, .footer--technical .footer__column-link:focus::before {
  opacity: 1;
}

.footer--technical .footer__phone {
  position: relative;
}

.footer--technical .footer__phone::before {
  content: "";
  display: block;
  width: 100%;
  height: 14px;
  position: absolute;
  top: 55%;
  left: 0;
  z-index: -1;
  background-color: #ffdf91;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.footer--technical .footer__phone:hover, .footer--technical .footer__phone:focus {
  color: #000000;
}

.footer--technical .footer__phone:hover::before, .footer--technical .footer__phone:focus::before {
  opacity: 1;
}

.footer__appeal {
  margin-bottom: 40px;
  font-size: 30px;
  line-height: 1.5;
  color: #000000;
  letter-spacing: -0.025em;
  font-weight: 700;
}

@media (min-width: 1200px) {
  .footer__appeal {
    max-width: 500px;
    margin-top: -16px;
    font-size: 42px;
  }
}

.footer__appeal a {
  color: #efb834;
}

.footer__appeal a:hover, .footer__appeal a:focus {
  outline: none;
  text-decoration: underline;
}

.footer__top {
  margin-bottom: 32px;
  padding-bottom: 32px;
  border-bottom: solid 1px rgba(0, 0, 0, 0.2);
}

@media (min-width: 768px) {
  .footer__top {
    margin-bottom: 56px;
    padding-bottom: 56px;
  }
}

@media (min-width: 1200px) {
  .footer__top {
    margin-bottom: 115px;
    padding-bottom: 130px;
  }
}

.footer__top p {
  margin: 8px 0 0;
  font-size: 16px;
  line-height: 1.33;
  color: #666666;
  letter-spacing: -0.025em;
}

@media (min-width: 768px) {
  .footer__top p {
    margin-top: 16px;
    font-size: 20px;
  }
}

@media (min-width: 1200px) {
  .footer__top p {
    margin-top: 32px;
    font-size: 24px;
  }
}

@media (min-width: 992px) {
  .footer__middle {
    margin-bottom: 56px;
  }
}

@media (min-width: 1200px) {
  .footer__middle {
    margin-bottom: 152px;
  }
}

.footer__email {
  font-size: 30px;
  line-height: 1.33;
  color: #efb834;
  font-weight: 300;
  letter-spacing: -0.025em;
  transition: opacity 0.3s ease;
}

@media (min-width: 768px) {
  .footer__email {
    font-size: 42px;
  }
}

@media (min-width: 1200px) {
  .footer__email {
    font-size: 54px;
  }
}

.footer__email:hover, .footer__email:focus {
  outline: none;
  color: #efb834;
  opacity: 0.7;
}

.footer__address {
  font-size: 14px;
  line-height: 1.44;
  color: #666666;
  font-style: normal;
}

@media (min-width: 1200px) {
  .footer__address {
    margin-bottom: 10px;
    font-size: 18px;
  }
}

.footer__phone {
  font-size: 14px;
  line-height: 1;
  color: #666666;
  transition: color 0.3s ease;
}

@media (min-width: 1200px) {
  .footer__phone {
    font-size: 18px;
  }
}

.footer__phone:hover, .footer__phone:focus {
  outline: none;
  color: #efb834;
}

.footer__phone:active {
  opacity: 0.7;
}

@media (min-width: 992px) {
  .footer__social {
    justify-content: flex-end;
    margin-right: 2.41vw;
  }
}

.footer__copyright {
  margin-bottom: 12px;
  font-size: 12px;
  line-height: 1.5;
  color: #999999;
  letter-spacing: 0.015em;
}

.footer__copyright span {
  color: #000000;
}

@media (min-width: 768px) {
  .footer__copyright {
    margin-bottom: 0;
  }
}

@media (min-width: 1200px) {
  .footer__copyright {
    font-size: 16px;
  }
}

.footer__design {
  font-size: 12px;
  line-height: 1.5;
  color: #999999;
  letter-spacing: 0.015em;
}

.footer__design span {
  color: #000000;
}

@media (min-width: 576px) {
  .footer__design {
    text-align: right;
  }
}

@media (min-width: 1200px) {
  .footer__design {
    font-size: 16px;
  }
}

.footer__column {
  margin-bottom: 24px;
}

@media (min-width: 992px) {
  .footer__column {
    margin-bottom: 0;
  }
}

.footer__column-title {
  margin-bottom: 24px;
  font-size: 24px;
  line-height: 1;
  color: #000000;
}

@media (min-width: 768px) {
  .footer__column-title {
    font-size: 30px;
  }
}

@media (min-width: 1200px) {
  .footer__column-title {
    margin-bottom: 36px;
  }
}

.footer__column-address {
  margin-bottom: 12px;
  font-size: 16px;
  line-height: 1.3;
  color: #666666;
  font-style: normal;
}

.footer__column-email {
  font-size: 24px;
  line-height: 1.3;
  color: #000000;
  transition: color 0.3s ease;
}

.footer__column-email:hover, .footer__column-email:focus {
  outline: none;
  color: #efb834;
}

.footer__column-info {
  margin-bottom: 32px;
}

.footer__column-menu {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer__column-item--current .footer__column-link {
  color: #000000;
}

@media (min-width: 1200px) {
  .footer__column-item {
    margin-bottom: 9px;
  }
}

.footer__column-link {
  font-size: 14px;
  line-height: 1.44;
  color: #666666;
  transition: color 0.3s ease;
}

@media (min-width: 1200px) {
  .footer__column-link {
    font-size: 18px;
  }
}

.footer__column-link:hover, .footer__column-link:focus {
  outline: none;
  color: #efb834;
}

.footer__column-link:active {
  opacity: 0.7;
}

.footer__logo {
  margin-bottom: 20px;
}

@media (min-width: 768px) {
  .footer__logo {
    margin-bottom: 0;
  }
}

.footer__hint {
  margin: 12px 0;
  font-size: 16px;
  line-height: 1;
  color: #999999;
}

@media (min-width: 768px) {
  .footer__hint {
    margin-top: 24px;
  }
}

@media (min-width: 992px) {
  .footer__hint {
    margin: 0 20px 0 0;
  }
}

.footer__bottom {
  padding-top: 40px;
}

.webpage--beige .footer {
  position: relative;
  background-image: none;
  background-color: transparent;
}

.webpage--beige .footer::before {
  content: "";
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: calc(100% - 30px);
  max-width: 1560px;
  border-top: solid 1px rgba(255, 255, 255, 0.2);
}

.webpage--beige .footer__email {
  font-family: "Cinzel", "Georgia", serif;
  color: #ffffff;
}

@media (min-width: 1200px) {
  .webpage--beige .footer__email {
    font-size: 60px;
    letter-spacing: -0.025em;
  }
}

.webpage--beige .footer__top {
  border-bottom: solid 1px rgba(255, 255, 255, 0.2);
}

.webpage--beige .footer__top p {
  color: #cccccc;
}

.webpage--beige .footer__column-title {
  font-size: 18px;
  color: #999999;
  text-transform: uppercase;
}

.webpage--beige .footer__column-link {
  color: #ffffff;
}

.webpage--beige .footer__column-link:hover, .webpage--beige .footer__column-link:focus {
  color: #e6da89;
}

.webpage--beige .footer__address {
  color: #ffffff;
}

.webpage--beige .footer__phone {
  color: #ffffff;
}

.webpage--beige .footer__phone:hover, .webpage--beige .footer__phone:focus {
  color: #e6da89;
}

.webpage--beige .footer__copyright span, .webpage--beige .footer__design span {
  color: #ffffff;
}

.webpage--modern .footer__appeal a {
  color: #57695a;
}

.webpage--modern .footer__column-email:hover, .webpage--modern .footer__column-email:focus {
  outline: none;
  color: #57695a;
}

.webpage--modern .footer__column-link:hover, .webpage--modern .footer__column-link:focus {
  outline: none;
  color: #57695a;
}

/* 3.2 Footer type 2 */
.footer-3 {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  width: 100%;
  height: 40px;
  padding: 8px 15px 8px;
  position: fixed;
  left: 0;
  bottom: 0;
  z-index: 2;
  transition: background-color 0.3s ease;
}

@media (min-width: 992px) {
  .footer-3 {
    height: 93px;
    padding: 0 50px 38px;
  }
}

.footer-3__socials {
  display: flex;
  align-items: center;
  justify-content: center;
  list-style: none;
  margin: 0;
  padding: 0;
}

@media (min-width: 992px) {
  .footer-3__socials {
    justify-content: flex-start;
  }
}

.footer-3__socials li + li {
  margin-left: 24px;
}

.footer-3__socials li a {
  display: block;
}

@media (min-width: 992px) {
  .footer-3__socials li a:hover svg {
    fill: #efb834;
  }
}

.footer-3__socials li a svg {
  width: 18px;
  height: 18px;
  fill: #ffffff;
  transition: fill 0.3s ease;
}

.footer-3__copyrights {
  font-size: 12px;
  line-height: 14px;
  text-transform: uppercase;
  letter-spacing: 0.35px;
  text-align: right;
  color: #ffffff;
  transition: color 0.3s ease;
}

@media (min-width: 992px) {
  .footer-3__copyrights {
    font-size: 14px;
    line-height: 22px;
  }
}

.footer-3__copyrights p {
  margin: 0;
}

.footer-3--dark .footer-3__socials li a svg {
  fill: #000000;
}

.footer-3--dark .footer-3__copyrights {
  color: #000000;
}

/*-------------------------------------------------------------------------------
  4. Main
-------------------------------------------------------------------------------*/
/* 4.1 Article preview */
@media (min-width: 992px) {
  .article-preview {
    position: relative;
    overflow: hidden;
  }
}

.article-preview:hover, .article-preview:focus {
  outline: none;
}

@media (min-width: 992px) {
  .article-preview:hover .article-preview__content, .article-preview:focus .article-preview__content {
    transform: translateY(0);
  }
}

@media (min-width: 768px) {
  .article-preview__image {
    height: 356px;
  }
}

.article-preview__image img {
  display: block;
  width: 100%;
  height: auto;
}

@media (min-width: 768px) {
  .article-preview__image img {
    height: 100%;
    object-fit: cover;
  }
}

.article-preview__content {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 20px 24px;
  border: solid 1px #d6d6d6;
  border-top: none;
  background-color: #fefae9;
  transition: transform 0.3s ease;
}

@media (min-width: 768px) {
  .article-preview__content {
    padding: 16px 12px;
  }
}

@media (min-width: 992px) {
  .article-preview__content {
    position: absolute;
    left: 0;
    top: 0;
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 32px;
    transform: translateY(100%);
    border-top: solid 1px #d6d6d6;
    z-index: 1;
  }
}

@media (min-width: 1200px) {
  .article-preview__content {
    padding: 44px;
  }
}

.article-preview__date {
  margin-bottom: 12px;
  font-size: 13px;
  line-height: 1;
  color: #666666;
}

.article-preview__heading {
  margin: 0 0 20px;
  font-size: 24px;
  line-height: 1.33;
  color: #000000;
  font-weight: 400;
}

@media (min-width: 768px) {
  .article-preview__heading {
    font-size: 20px;
  }
}

@media (min-width: 992px) {
  .article-preview__heading {
    font-size: 24px;
  }
}

.article-preview__text {
  margin-bottom: 24px;
  font-size: 16px;
  line-height: 1.625;
  color: #666666;
}

@media (min-width: 768px) {
  .article-preview__text {
    font-size: 14px;
  }
}

@media (min-width: 992px) {
  .article-preview__text {
    font-size: 16px;
  }
}

.article-preview__btn {
  font-size: 14px;
  line-height: 1;
  color: #57695a;
  font-weight: 600;
  letter-spacing: 0.025em;
  text-transform: uppercase;
  margin-top: auto;
}

.article-preview__btn:hover, .article-preview__btn:focus {
  outline: none;
  color: #efb834;
}

.article-preview__btn:active {
  opacity: none;
}

.article-preview__outer-content {
  display: none;
  position: absolute;
  bottom: 0;
  left: 0;
  padding: 12px;
  z-index: 1;
  max-width: 83%;
}

@media (min-width: 992px) {
  .article-preview__outer-content {
    display: block;
    padding: 15px 35px 33px 40px;
    max-width: 100%;
  }
}

.article-preview__tag {
  display: inline-block;
  font-size: 12px;
  line-height: 12px;
  text-transform: uppercase;
  color: #ffffff;
  padding: 3.5px 5px;
  margin-bottom: 6px;
  background-color: #f5480c;
}

.article-preview__title {
  font-size: 30px;
  line-height: 36px;
  color: #ffffff;
  margin-bottom: 21px;
}

.article-preview__publish-date {
  font-size: 14px;
  color: #ffffff;
}

/* 4.2 Statistics */
@media (min-width: 992px) {
  .statistics--column .row {
    height: 100%;
  }
}

@media (min-width: 992px) {
  .statistics--column .statistics__item {
    flex: none;
  }
}

@media (min-width: 992px) {
  .statistics--column .statistics__item-value {
    min-width: 119px;
    font-size: 48px;
  }
}

@media (min-width: 992px) {
  .statistics--column .statistics__item-text {
    font-size: 14px;
  }
}

@media (min-width: 992px) {
  .statistics--column .statistics__item + .statistics__item {
    margin-top: 16px;
  }
}

.statistics--color-invert .statistics__item-value {
  font-weight: 400;
  color: #999999;
}

.statistics--color-invert .statistics__item-text {
  color: #000000;
}

.statistics--m {
  margin-top: 60px;
  margin-bottom: 60px;
}

@media (min-width: 1200px) {
  .statistics--m {
    margin-top: 70px;
    margin-bottom: 150px;
  }
}

.statistics__item {
  text-align: center;
}

.statistics__item + .statistics__item {
  margin-top: 24px;
}

@media (min-width: 576px) {
  .statistics__item {
    display: flex;
    text-align: left;
  }
  .statistics__item + .statistics__item {
    margin-top: 0;
  }
  .statistics__item:last-child {
    margin-top: 24px;
  }
}

@media (min-width: 992px) {
  .statistics__item {
    justify-content: flex-start;
  }
  .statistics__item:last-child {
    margin-top: 0;
  }
}

.statistics__item-value {
  margin-bottom: 6px;
  font-size: 36px;
  line-height: 1;
  color: #000000;
  font-weight: 300;
}

@media (min-width: 576px) {
  .statistics__item-value {
    margin-bottom: 0;
    margin-right: 24px;
    font-size: 48px;
    align-self: center;
  }
}

@media (min-width: 1200px) {
  .statistics__item-value {
    font-size: 60px;
  }
}

.statistics__item-text {
  font-size: 14px;
  line-height: 1.625;
  color: #999999;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.statistics__item-text br {
  display: none;
}

@media (min-width: 576px) {
  .statistics__item-text {
    align-self: center;
  }
  .statistics__item-text br {
    display: block;
  }
}

@media (min-width: 1200px) {
  .statistics__item-text {
    font-size: 16px;
  }
}

.webpage--beige .statistics__item-value {
  font-family: "Cinzel", "Georgia", serif;
  color: #4d524b;
}

.webpage--beige .statistics__item-text {
  color: #666666;
}

/* 4.3 Phone block */
.phone-block {
  display: flex;
}

.phone-block--theme-orange .phone-block__icon {
  background-color: #efb834;
}

.phone-block--theme-orange .phone-block__icon svg {
  fill: #ffffff;
}

.phone-block--theme-beige .phone-block__icon {
  background-color: #4d524b;
}

.phone-block--theme-beige .phone-block__icon svg {
  fill: #e6da89;
}

.phone-block--theme-beige .phone-block__number:hover, .phone-block--theme-beige .phone-block__number:focus {
  outline: none;
  color: #aeab98;
}

.phone-block__icon {
  display: flex;
  justify-content: center;
  width: 50px;
  height: 50px;
  margin-right: 20px;
  border-radius: 50%;
  background-color: #faece7;
  flex-shrink: 0;
  align-self: center;
}

.phone-block__icon svg {
  fill: #efb834;
  align-self: center;
}

.phone-block__content {
  align-self: center;
}

.phone-block__hint {
  margin-bottom: 10px;
  font-size: 12px;
  line-height: 1;
  color: #666666;
  letter-spacing: 0.05em;
  text-transform: uppercase;
}

.phone-block__number {
  display: inline-block;
  vertical-align: top;
  font-size: 18px;
  line-height: 1;
  color: #000000;
  letter-spacing: 0.025em;
  transition: color 0.3s ease;
}

@media (min-width: 992px) {
  .phone-block__number {
    font-size: 24px;
  }
}

.phone-block__number:hover, .phone-block__number:focus {
  outline: none;
  color: #efb834;
}

/* 4.4 Work card */
.work-card--compact, .work-card--masonry {
  position: relative;
  padding: 0;
  box-sizing: border-box;
}

.work-card--compact .work-card__content, .work-card--masonry .work-card__content {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  padding: 36px 36px 30px;
  pointer-events: none;
}

@media (min-width: 768px) {
  .work-card--compact .work-card__content, .work-card--masonry .work-card__content {
    padding: 48px 48px 42px;
  }
}

@media (min-width: 1200px) {
  .work-card--compact .work-card__content, .work-card--masonry .work-card__content {
    padding: 64px 64px 60px;
  }
}

.work-card--compact .work-card__image, .work-card--masonry .work-card__image {
  position: relative;
  margin: 0;
}

.work-card--compact .work-card__image::before, .work-card--masonry .work-card__image::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  display: block;
  width: 100%;
  height: 100%;
  background-color: #efb834;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.work-card--compact .work-card__image:hover, .work-card--compact .work-card__image:focus, .work-card--masonry .work-card__image:hover, .work-card--masonry .work-card__image:focus {
  outline: none;
  opacity: 1;
}

.work-card--compact .work-card__image:hover::before, .work-card--compact .work-card__image:focus::before, .work-card--masonry .work-card__image:hover::before, .work-card--masonry .work-card__image:focus::before {
  opacity: 1;
}

.work-card--compact .work-card__year, .work-card--masonry .work-card__year {
  margin-bottom: 20px;
  font-size: 18px;
  line-height: 1;
  color: #fefae9;
}

.work-card--compact .work-card__tags, .work-card--masonry .work-card__tags {
  margin-top: auto;
  font-size: 18px;
  color: #fefae9;
}

.work-card--compact .work-card__heading, .work-card--masonry .work-card__heading {
  max-width: 380px;
  margin: 0;
  font-weight: 700;
}

@media (min-width: 1200px) {
  .work-card--compact .work-card__heading, .work-card--masonry .work-card__heading {
    font-size: 36px !important;
  }
}

.work-card--compact .work-card__heading a, .work-card--masonry .work-card__heading a {
  color: #fefae9;
}

.work-card--masonry:hover .work-card__content {
  opacity: 1;
}

.work-card--masonry .work-card__content {
  padding: 36px 36px 30px;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

@media (min-width: 768px) {
  .work-card--masonry .work-card__content {
    padding: 48px 48px 42px;
  }
}

.work-card--grid {
  border: 4px solid #000000;
  box-sizing: border-box;
}

.work-card--grid .work-card__image {
  height: 450px;
  padding: 0;
  margin-bottom: 0;
  overflow: hidden;
}

@media (min-width: 1200px) {
  .work-card--grid .work-card__image {
    height: 780px;
    padding: 50px;
  }
}

.work-card--grid .work-card__image:hover img {
  transform: scale(1.3);
}

.work-card--grid .work-card__image img {
  height: 100%;
  object-fit: cover;
}

.work-card--grid .work-card__content {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  border-top: 1px solid #000000;
}

@media (min-width: 992px) {
  .work-card--grid .work-card__content {
    flex-direction: row;
  }
}

.work-card--grid .work-card__content-wrapper {
  padding: 45px 60px 25px;
}

.work-card--grid .work-card__tags {
  font-size: 14px;
  line-height: 34px;
  letter-spacing: 0.05em;
  text-transform: uppercase;
  color: #666666;
  margin-bottom: 10px;
}

.work-card--grid .work-card__heading a {
  font-size: 36px;
  letter-spacing: -0.025em;
  color: #000000;
  text-transform: none;
  cursor: pointer;
}

@media (min-width: 992px) {
  .work-card--grid .work-card__heading a {
    font-size: 48px;
  }
}

.work-card--grid .work-card__button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  border-top: 1px solid #000000;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

@media (min-width: 992px) {
  .work-card--grid .work-card__button {
    width: 212px;
    border-left: 1px solid #000000;
  }
  .work-card--grid .work-card__button:hover, .work-card--grid .work-card__button:focus {
    outline: none;
    background-color: #ffdf91;
  }
}

.work-card--grid .work-card__button svg {
  width: 96px;
  height: 54px;
}

.work-card__image {
  display: block;
  margin-bottom: 24px;
  overflow: hidden;
  transition: opacity 0.3s ease;
}

@media (min-width: 1200px) {
  .work-card__image {
    margin-bottom: 43px;
  }
}

.work-card__image:hover, .work-card__image:focus {
  outline: none;
  opacity: 0.7;
}

.work-card__image:hover img, .work-card__image:focus img {
  transform: scale(1.1);
}

.work-card__image img {
  display: block;
  width: 100%;
  height: auto;
  transition: transform 0.3s ease;
}

.work-card__tags {
  margin-bottom: 12px;
  font-size: 16px;
  line-height: 1;
  color: #efb834;
}

@media (min-width: 1200px) {
  .work-card__tags {
    margin-bottom: 18px;
  }
}

.work-card__heading {
  margin: 0 0 20px;
  font-size: 24px;
  line-height: 1;
  color: #000000;
  letter-spacing: 0.025em;
  font-weight: 400;
  text-transform: uppercase;
}

@media (min-width: 1200px) {
  .work-card__heading {
    margin-bottom: 26px;
    font-size: 30px;
  }
}

.work-card__heading a {
  color: #000000;
}

.work-card__heading a:hover, .work-card__heading a:focus {
  outline: none;
  text-decoration: underline;
}

.work-card__text {
  font-size: 16px;
  line-height: 1.625;
  color: #666666;
}

.webpage--modern .work-card--compact .work-card__image::before {
  background-color: #57695a;
}

/* 4.5 Slide content */
.slide-content {
  box-sizing: border-box;
  padding: 35px 0 10px;
  background-color: #ffffff;
  transition: all 0.3s ease;
}

@media (min-width: 576px) {
  .slide-content {
    padding: 20px;
  }
  .slide-content:hover {
    background-color: #efb834;
  }
  .slide-content:hover .slide-content__heading a {
    color: #ffffff;
  }
  .slide-content:hover .slide-content__detail {
    color: #ffffff;
  }
}

@media (min-width: 992px) {
  .slide-content {
    padding: 32px;
  }
}

@media (min-width: 1200px) {
  .slide-content {
    padding: 48px;
  }
}

@media (min-width: 1560px) {
  .slide-content {
    padding: 67px 63px 43px;
  }
}

.slide-content--thumb {
  cursor: pointer;
}

@media (min-width: 768px) {
  .slide-content--thumb {
    background-color: transparent;
    background-image: linear-gradient(to top, rgba(0, 0, 0, 0.6), transparent);
  }
}

@media (min-width: 1200px) {
  .slide-content--thumb {
    padding: 28px;
  }
}

@media (min-width: 768px) {
  .slide-content--thumb .slide-content__detail {
    color: #ffffff;
  }
}

@media (min-width: 768px) {
  .slide-content--thumb .slide-content__heading {
    color: #ffffff;
    font-weight: 300;
  }
  .slide-content--thumb .slide-content__heading a {
    color: #ffffff;
  }
}

.slide-content--thumb:hover, .slide-content--thumb:focus {
  background-color: #ffffff;
  background-image: none;
  transition: background-color 0.3s ease;
}

.slide-content--thumb:hover .slide-content__detail, .slide-content--thumb:focus .slide-content__detail {
  color: #666666;
}

.slide-content--thumb:hover .slide-content__heading, .slide-content--thumb:focus .slide-content__heading {
  color: #000000;
}

.slide-content--thumb:hover .slide-content__heading a, .slide-content--thumb:focus .slide-content__heading a {
  color: #000000;
}

.slide-content--thumb:hover .slide-content__heading a:hover, .slide-content--thumb:hover .slide-content__heading a:focus, .slide-content--thumb:focus .slide-content__heading a:hover, .slide-content--thumb:focus .slide-content__heading a:focus {
  color: #efb834;
}

@media (min-width: 768px) {
  .slide-content--thumb:hover .slide-content__heading a, .slide-content--thumb:focus .slide-content__heading a {
    color: #000000;
  }
  .slide-content--thumb:hover .slide-content__heading a:hover, .slide-content--thumb:hover .slide-content__heading a:focus, .slide-content--thumb:focus .slide-content__heading a:hover, .slide-content--thumb:focus .slide-content__heading a:focus {
    color: #efb834;
  }
}

.slide-content__detail {
  margin-bottom: 6px;
  transition: color 0.3s ease;
  font-size: 12px;
  line-height: 1;
  color: #666666;
  letter-spacing: 0.025em;
}

@media (min-width: 992px) {
  .slide-content__detail {
    margin-bottom: 8px;
    font-size: 14px;
  }
}

@media (min-width: 1200px) {
  .slide-content__detail {
    margin-bottom: 20px;
    font-size: 16px;
  }
}

@media (min-width: 1560px) {
  .slide-content__detail {
    margin-bottom: 24px;
  }
}

.slide-content__heading {
  font-size: 20px;
  line-height: 1;
  color: #000000;
}

.slide-content__heading a {
  color: #000000;
  transition: color 0.3s ease;
}

.slide-content__heading a:hover, .slide-content__heading a:focus {
  outline: none;
  color: #efb834;
}

@media (min-width: 992px) {
  .slide-content__heading {
    font-size: 30px;
  }
}

@media (min-width: 1200px) {
  .slide-content__heading {
    font-size: 36px;
  }
}

@media (min-width: 1560px) {
  .slide-content__heading {
    font-size: 40px;
  }
}

/* 4.6 Slide */
.slide {
  position: relative;
}

.slide img {
  display: block;
  width: 100%;
  height: auto;
}

@media (min-width: 576px) {
  .slide__content {
    position: absolute;
    left: -1px;
    bottom: -1px;
  }
}

@media (min-width: 1200px) {
  .slide__content {
    min-width: 475px;
  }
}

/* 4.7 Slider */
.slider {
  margin-bottom: 0 !important;
}

.slider .swiper-pagination {
  position: static;
  display: flex;
  justify-content: center;
}

@media (min-width: 576px) {
  .slider .swiper-pagination {
    bottom: -48px;
  }
}

@media (min-width: 1200px) {
  .slider .swiper-pagination {
    bottom: -104px;
  }
}

.slider .swiper-pagination .swiper-pagination-bullet {
  width: 8px;
  height: 8px;
  margin: 0 4px;
  appearance: none;
  border-radius: 4px;
  background-color: #cccccc;
  opacity: 1;
  transition: width 0.3s ease, background-color 0.3s ease;
}

@media (min-width: 576px) {
  .slider .swiper-pagination .swiper-pagination-bullet {
    margin: 0 11px;
  }
}

.slider .swiper-pagination .swiper-pagination-bullet-active {
  width: 36px;
  background-color: #efb834;
}

.slider--single .swiper-pagination {
  margin-top: 20px;
}

@media (min-width: 768px) {
  .slider--single .swiper-pagination {
    margin-top: 40px;
  }
}

@media (min-width: 1200px) {
  .slider--single .swiper-pagination {
    margin-top: 95px;
  }
}

.slider--single .swiper-slide-next .slide__content {
  opacity: 0;
}

@media (min-width: 768px) {
  .slider--single .slider__item {
    width: 77%;
  }
}

@media (min-width: 1200px) {
  .slider--single .slider__item {
    width: 75.328%;
  }
}

@media (min-width: 768px) {
  .slider--single .slider__nav {
    top: calc(50% - 20px);
    left: 0;
    right: 0;
    transform: translateY(-50%);
    z-index: 1;
  }
}

@media (min-width: 1200px) {
  .slider--single .slider__nav {
    top: calc(50% - 47px);
  }
}

.slider--carousel .swiper-scrollbar {
  position: relative;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 1px;
  margin-top: 90px;
  background-color: rgba(0, 0, 0, 0.15);
}

.slider--carousel .swiper-scrollbar-drag {
  top: -1px;
  height: 3px;
  background-color: #efb834;
}

.slider--carousel .slider__item {
  width: 100%;
  box-sizing: border-box;
}

@media (min-width: 576px) {
  .slider--carousel .slider__item {
    width: 70%;
  }
}

@media (min-width: 992px) {
  .slider--carousel .slider__item {
    width: 40%;
  }
}

@media (min-width: 1200px) {
  .slider--carousel .slider__item {
    width: 35.5%;
  }
}

.slider--with-thumbs {
  padding-top: 60px;
}

@media (min-width: 768px) {
  .slider--with-thumbs {
    padding-top: 0;
  }
}

.slider--with-thumbs .slider__nav {
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
}

@media (min-width: 768px) {
  .slider--with-thumbs .slider__nav {
    top: 50%;
    left: 0;
    right: 0;
    transform: translateY(-50%);
    z-index: 1;
  }
}

.slider--with-thumbs .slider__nav-btn {
  width: 18px;
  height: 32px;
  padding: 0;
  border: none;
  overflow: hidden;
  background-color: transparent;
  appearance: none;
  cursor: pointer;
}

.slider--with-thumbs .slider__nav-btn:hover, .slider--with-thumbs .slider__nav-btn:focus {
  outline: none;
  background-color: transparent !important;
  border-color: transparent !important;
}

.slider--with-thumbs .slider__nav-btn:hover svg, .slider--with-thumbs .slider__nav-btn:focus svg {
  fill: #efb834 !important;
}

@media (min-width: 768px) {
  .slider--with-thumbs .slider__nav-btn {
    border: solid 1px #ffffff !important;
  }
  .slider--with-thumbs .slider__nav-btn:hover, .slider--with-thumbs .slider__nav-btn:focus {
    background-color: #efb834 !important;
    border: solid 1px #efb834;
  }
  .slider--with-thumbs .slider__nav-btn:hover svg, .slider--with-thumbs .slider__nav-btn:focus svg {
    fill: #ffffff !important;
  }
}

.slider--with-thumbs .slider__nav-btn.swiper-button-disabled {
  pointer-events: none;
  opacity: 0.5;
}

@media (min-width: 768px) {
  .slider--with-thumbs .slider__nav-btn.swiper-button-disabled {
    border: solid 1px #ffffff;
    background-color: transparent !important;
  }
  .slider--with-thumbs .slider__nav-btn.swiper-button-disabled svg {
    fill: #ffffff !important;
  }
}

.slider--with-thumbs .slider__nav-btn svg {
  position: relative;
  left: 50%;
  display: block;
  width: auto;
  height: 100%;
  transform: translateX(-50%);
  transition: fill 0.3s ease;
}

@media (min-width: 768px) {
  .slider--with-thumbs .slider__nav-btn svg {
    position: static;
    width: 34px;
    height: auto;
    transform: translateX(0);
    fill: #ffffff;
  }
}

@media (min-width: 768px) {
  .slider--with-thumbs .slider__nav-btn {
    position: absolute;
    top: 0;
    transform: translateY(-50%);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    border: solid 1px #ffffff;
    transition: background-color 0.3s ease, border-color 0.3s ease;
  }
  .slider--with-thumbs .slider__nav-btn:hover, .slider--with-thumbs .slider__nav-btn:focus {
    background-color: #ffffff;
  }
  .slider--with-thumbs .slider__nav-btn:hover svg, .slider--with-thumbs .slider__nav-btn:focus svg {
    fill: #000000;
  }
}

.slider__nav {
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
}

@media (min-width: 768px) {
  .slider__nav {
    top: 50%;
    left: 0;
    right: 0;
    transform: translateY(-50%);
    z-index: 1;
  }
}

.slider__nav-btn {
  width: 18px;
  height: 32px;
  padding: 0;
  border: none;
  overflow: hidden;
  background-color: transparent;
  appearance: none;
  cursor: pointer;
}

.slider__nav-btn:hover, .slider__nav-btn:focus {
  outline: none;
  background-color: #efb834 !important;
  border-color: #efb834 !important;
}

.slider__nav-btn:hover svg, .slider__nav-btn:focus svg {
  fill: #ffffff !important;
}

.slider__nav-btn--next {
  margin-left: 12px;
}

@media (min-width: 768px) {
  .slider__nav-btn--next {
    right: 3.125vw;
    margin: 0;
  }
}

@media (min-width: 768px) {
  .slider__nav-btn--prev {
    left: 3.125vw;
  }
}

.slider__nav-btn.swiper-button-disabled {
  pointer-events: none;
  opacity: 0.5;
}

@media (min-width: 768px) {
  .slider__nav-btn.swiper-button-disabled {
    border: solid 1px #ffffff;
    background-color: transparent !important;
  }
  .slider__nav-btn.swiper-button-disabled svg {
    fill: #ffffff !important;
  }
}

.slider__nav-btn svg {
  position: relative;
  left: 50%;
  display: block;
  width: auto;
  height: 100%;
  transform: translateX(-50%);
  transition: fill 0.3s ease;
}

@media (min-width: 768px) {
  .slider__nav-btn svg {
    position: static;
    width: 34px;
    height: auto;
    transform: translateX(0);
    fill: #ffffff;
  }
}

@media (min-width: 768px) {
  .slider__nav-btn {
    position: absolute;
    top: 0;
    transform: translateY(-50%);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    border: solid 1px #ffffff;
    transition: background-color 0.3s ease, border-color 0.3s ease;
  }
  .slider__nav-btn:hover, .slider__nav-btn:focus {
    background-color: #ffffff;
  }
  .slider__nav-btn:hover svg, .slider__nav-btn:focus svg {
    fill: #000000;
  }
}

.webpage--modern .slider .swiper-pagination .swiper-pagination-bullet-active {
  background-color: #57695a;
}

/* 4.8 Project meta */
.project-meta {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-wrap: wrap;
}

.project-meta--line .project-meta__item {
  padding: 0 10px;
  text-align: center;
}

@media (min-width: 768px) {
  .project-meta--line .project-meta__item {
    width: 25%;
    margin: 0 !important;
  }
}

.project-meta--table {
  display: block;
}

.project-meta--table .project-meta__item {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin: 0 !important;
  padding: 10px 0;
  border-bottom: solid 1px rgba(0, 0, 0, 0.1);
}

.project-meta--table .project-meta__item:last-child {
  border-bottom: none;
}

.project-meta--table .project-meta__item-title {
  margin: 0;
  align-self: center;
}

@media (min-width: 576px) {
  .project-meta--table .project-meta__item-title {
    font-size: 14px;
  }
}

.project-meta--table .project-meta__item-text {
  text-align: right;
  align-self: center;
}

@media (min-width: 576px) {
  .project-meta--table .project-meta__item-text {
    font-size: 16px;
  }
}

@media (min-width: 1200px) {
  .project-meta {
    padding-top: 10px;
  }
}

.project-meta__item {
  width: 50%;
  padding-right: 10px;
}

@media (min-width: 1200px) {
  .project-meta__item {
    padding: 0 22px;
  }
}

.project-meta__item:nth-child(n + 3) {
  margin-top: 30px;
}

@media (min-width: 1200px) {
  .project-meta__item:nth-child(n + 3) {
    margin-top: 52px;
  }
}

.project-meta__item-title {
  margin-bottom: 12px;
  font-size: 12px;
  line-height: 1;
  color: #999999;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

@media (min-width: 1200px) {
  .project-meta__item-title {
    margin-bottom: 24px;
    font-size: 14px;
  }
}

.project-meta__item-text {
  font-size: 14px;
  line-height: 1.66;
  color: #000000;
}

@media (min-width: 1200px) {
  .project-meta__item-text {
    font-size: 18px;
  }
}

/* 4.9 Twitter block */
@media (min-width: 768px) {
  .twitter-block--small .twitter-block__wrapper {
    padding: 40px;
  }
}

@media (min-width: 992px) {
  .twitter-block--small .twitter-block__wrapper {
    padding: 40px;
  }
}

@media (min-width: 1200px) {
  .twitter-block--small .twitter-block__wrapper {
    padding: 65px 57px 65px 70px;
  }
}

.twitter-block--small .twitter-block__text {
  font-size: 14px;
  line-height: 1.33;
  color: #000000;
  font-style: italic;
}

@media (min-width: 576px) {
  .twitter-block--small .twitter-block__text {
    font-size: 20px;
  }
}

@media (min-width: 992px) {
  .twitter-block--small .twitter-block__text {
    font-size: 20px;
  }
}

@media (min-width: 1200px) {
  .twitter-block--small .twitter-block__text {
    font-size: 30px;
  }
}

.twitter-block--sidebar {
  max-width: 100%;
}

@media (min-width: 992px) {
  .twitter-block--sidebar {
    max-width: 300px;
  }
}

.twitter-block--sidebar .twitter-block__wrapper {
  padding: 30px !important;
}

.twitter-block--sidebar .twitter-block__header {
  margin-bottom: 32px !important;
}

.twitter-block--sidebar .twitter-block__author-image {
  width: 45px !important;
  height: 45px !important;
}

.twitter-block--sidebar .twitter-block__author-meta, .twitter-block--sidebar .twitter-block__author-name {
  font-size: 16px !important;
}

.twitter-block--sidebar .twitter-block__author-info {
  font-size: 12px !important;
}

.twitter-block--sidebar .twitter-block__text {
  margin-bottom: 30px;
  font-size: 18px !important;
}

.twitter-block--sidebar .twitter-block__social svg {
  width: 24px !important;
}

.twitter-block--sidebar time {
  font-size: 12px !important;
}

.twitter-block--technical .twitter-block__wrapper {
  padding: 0;
  border: none;
}

.twitter-block__heading {
  margin-bottom: 40px;
  font-weight: 400;
}

@media (min-width: 1200px) {
  .twitter-block__heading {
    margin-bottom: 92px;
    font-size: 60px;
    line-height: 1;
    letter-spacing: -0.025em;
  }
}

.twitter-block__wrapper {
  padding: 20px;
  border: solid 1px #d6d6d6;
}

@media (min-width: 576px) {
  .twitter-block__wrapper {
    padding: 40px;
  }
}

@media (min-width: 768px) {
  .twitter-block__wrapper {
    padding: 60px;
  }
}

@media (min-width: 992px) {
  .twitter-block__wrapper {
    padding: 80px;
  }
}

@media (min-width: 1200px) {
  .twitter-block__wrapper {
    padding: 93px 170px 105px;
  }
}

.twitter-block__header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

@media (min-width: 576px) {
  .twitter-block__header {
    margin-bottom: 40px;
  }
}

@media (min-width: 992px) {
  .twitter-block__header {
    margin-bottom: 53px;
  }
}

.twitter-block__author {
  display: flex;
}

.twitter-block__author-image {
  width: 40px;
  height: 40px;
  margin-right: 12px;
  align-self: center;
  border-radius: 50%;
  overflow: hidden;
}

@media (min-width: 576px) {
  .twitter-block__author-image {
    width: 60px;
    height: 60px;
    margin-right: 20px;
  }
}

.twitter-block__author-image img {
  display: block;
  width: 100%;
  height: auto;
}

.twitter-block__author-meta {
  font-size: 14px;
  line-height: 1;
  color: #000000;
  align-self: center;
}

.twitter-block__author-name {
  margin-bottom: 6px;
}

@media (min-width: 576px) {
  .twitter-block__author-name {
    font-size: 20px;
  }
}

.twitter-block__author-info {
  font-size: 12px;
  color: #999999;
  letter-spacing: 0.015em;
}

@media (min-width: 576px) {
  .twitter-block__author-info {
    font-size: 16px;
  }
}

.twitter-block__author-info a {
  color: #999999;
  transition: color 0.3s ease;
}

.twitter-block__author-info a:hover, .twitter-block__author-info a:focus {
  outline: none;
  color: #00ccff;
}

.twitter-block__author-info a:active {
  opacity: 0.7;
}

.twitter-block__social {
  align-self: center;
}

.twitter-block__social svg {
  display: block;
  width: 24px;
  height: auto;
  fill: #00ccff;
  transition: fill 0.3s ease;
}

@media (min-width: 576px) {
  .twitter-block__social svg {
    width: 34px;
  }
}

.twitter-block__text {
  font-size: 14px;
  line-height: 1.64;
  color: #000000;
  font-style: italic;
}

@media (min-width: 576px) {
  .twitter-block__text {
    font-size: 20px;
  }
}

@media (min-width: 992px) {
  .twitter-block__text {
    font-size: 28px;
  }
}

.twitter-block__text a {
  color: #00ccff;
  transition: color 0.3s ease;
}

.twitter-block__text a:hover, .twitter-block__text a:focus {
  outline: none;
  color: #33d6ff;
}

.twitter-block__text a:active {
  opacity: 0.7;
}

.twitter-block time {
  font-size: 14px;
  line-height: 1;
  color: #999999;
  letter-spacing: 0.015em;
}

@media (min-width: 576px) {
  .twitter-block time {
    font-size: 16px;
  }
}

.webpage--beige .twitter-block__wrapper {
  border-color: #aeab98;
}

/* 4.10 Like */
.liked {
  width: 80px;
}

.liked__btn {
  display: flex;
  justify-content: center;
  width: 80px;
  height: 80px;
  margin-bottom: 20px;
  border-radius: 50%;
  border: solid 1px #d6d6d6;
}

.liked__btn svg {
  align-self: center;
  fill: #efb834;
  transition: transform 0.3s ease;
}

.liked__btn:hover, .liked__btn:focus {
  outline: none;
}

.liked__btn:hover svg, .liked__btn:focus svg {
  transform: scale(1.1);
}

.liked__btn:active {
  opacity: 0.7;
}

.liked__text {
  font-size: 16px;
  line-height: 1;
  color: #000000;
  letter-spacing: 0.015em;
  text-align: center;
}

/* 4.11 Testimonials */
.testimonials__header {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40px;
}

@media (min-width: 1200px) {
  .testimonials__header {
    margin-bottom: 78px;
  }
}

.testimonials__heading {
  font-weight: 400;
}

.testimonials__nav {
  display: flex;
  margin-left: 20px;
}

.testimonials__nav-btn {
  width: 18px;
  height: 32px;
  padding: 0;
  border: none;
  overflow: hidden;
  background-color: transparent;
  appearance: none;
  cursor: pointer;
}

@media (min-width: 1200px) {
  .testimonials__nav-btn {
    width: 20px;
    height: 36px;
  }
}

.testimonials__nav-btn + .testimonials__nav-btn {
  margin-left: 12px;
}

@media (min-width: 1200px) {
  .testimonials__nav-btn + .testimonials__nav-btn {
    margin-left: 50px;
  }
}

.testimonials__nav-btn svg {
  position: relative;
  left: 50%;
  display: block;
  width: auto;
  height: 100%;
  transform: translateX(-50%);
  transition: fill 0.3s ease;
}

.testimonials__nav-btn:hover, .testimonials__nav-btn:focus {
  outline: none;
}

.testimonials__nav-btn:hover svg, .testimonials__nav-btn:focus svg {
  fill: #efb834;
}

.testimonials__carousel .swiper-wrapper {
  padding: 1px 0;
}

.testimonials__item {
  width: calc(100% - 2px);
  box-sizing: border-box;
}

@media (min-width: 992px) {
  .testimonials__item {
    width: calc(50% - 31px);
  }
}

.testimonials + .partners {
  margin-top: 60px;
}

@media (min-width: 1200px) {
  .testimonials + .partners {
    margin-top: 120px;
  }
}

.webpage--beige .testimonials--simple .testimonials__heading {
  font-family: "Cinzel", "Georgia", serif;
  text-transform: uppercase;
}

.th-testimonials__inner {
  padding: 40px;
}

@media (min-width: 1200px) {
  .th-testimonials__inner {
    padding: 120px 80px;
  }
}

@media (min-width: 1560px) {
  .th-testimonials__inner {
    padding: 120px 110px;
  }
}

.th-testimonials__heading {
  margin-bottom: 32px;
  font-weight: 400;
}

@media (min-width: 1200px) {
  .th-testimonials__heading {
    margin-bottom: 72px;
  }
}

.th-testimonials__slider .swiper-pagination {
  position: static;
  display: flex;
  align-items: center;
  margin-top: 60px;
}

.th-testimonials__slider .swiper-pagination .swiper-pagination-bullet {
  position: relative;
  width: 25px;
  height: 25px;
  border: solid 1px transparent;
  background-color: transparent;
  opacity: 1;
}

.th-testimonials__slider .swiper-pagination .swiper-pagination-bullet::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translateY(-50%) translateX(-50%);
  width: 9px;
  height: 9px;
  border-radius: 50%;
  background-color: #cccccc;
}

.th-testimonials__slider .swiper-pagination .swiper-pagination-bullet-active {
  border-color: #ffdf91;
}

.th-testimonials__slider .swiper-pagination .swiper-pagination-bullet-active::before {
  background-color: #ffdf91;
}

/* 4.12 Simplicity */
.simplicity__line {
  width: calc(100% - 30px);
  margin: 0 auto;
  padding-bottom: 60px;
}

@media (min-width: 1200px) {
  .simplicity__line {
    padding-bottom: 134px;
  }
}

.simplicity__image {
  position: relative;
  margin-bottom: 40px;
  margin-top: -60px;
  margin-left: 20px;
}

@media (min-width: 992px) {
  .simplicity__image {
    margin-bottom: 4.6875vw;
    padding-bottom: 7.135vw;
  }
}

.simplicity__image img {
  display: block;
  max-width: 100%;
  height: auto;
}

.simplicity__circle-text {
  position: absolute;
  z-index: 1;
  left: 0;
  bottom: 0;
  width: 180px;
  height: 180px;
  animation: text-rotate 9s linear infinite;
}

@media (min-width: 1200px) {
  .simplicity__circle-text {
    width: 226px;
    height: 226px;
    margin-left: -50px;
  }
}

.simplicity__circle-text img {
  display: block;
  width: 100%;
  height: auto;
}

.simplicity__circle-text:hover {
  animation-play-state: paused;
}

.simplicity__content {
  padding: 20px;
}

@media (min-width: 768px) {
  .simplicity__content {
    display: flex;
    flex-direction: column;
  }
}

.simplicity__preheading {
  margin-bottom: 12px;
  font-size: 16px;
  line-height: 1;
  color: #fefae9;
  text-transform: uppercase;
}

.simplicity__heading {
  color: #fefae9;
  margin: 0 0 12px;
  font-weight: 400;
}

.simplicity__heading--upper {
  text-transform: uppercase;
}

@media (min-width: 1200px) {
  .simplicity__heading {
    max-width: 500px;
  }
}

.simplicity__subheading {
  color: #fefae9;
  margin-top: 40px;
  margin-bottom: 20px;
  font-size: 20px;
  letter-spacing: -0.015em;
}

@media (min-width: 768px) {
  .simplicity__subheading {
    max-width: 320px;
  }
}

@media (min-width: 1200px) {
  .simplicity__subheading {
    min-width: 370px;
    font-size: 24px;
  }
}

.simplicity__text {
  margin-bottom: 12px;
  font-size: 16px;
  line-height: 1.77;
  color: #fefae9;
}

@media (min-width: 1200px) {
  .simplicity__text {
    font-size: 18px;
  }
}

.simplicity__notice {
  margin-bottom: 20px;
  font-size: 14px;
  line-height: 1.5;
  color: #000000;
}

@media (min-width: 768px) {
  .simplicity__notice {
    margin-top: auto;
  }
}

@media (min-width: 1200px) {
  .simplicity__notice {
    margin-bottom: 45px;
    font-size: 16px;
  }
}

.simplicity__phone {
  margin-bottom: 60px;
}

@media (min-width: 768px) {
  .simplicity__phone {
    margin-bottom: 0;
  }
}

.simplicity__btn {
  align-self: flex-start;
}

.simplicity__statistics {
  margin-top: 40px;
  padding-top: 40px;
  border-top: solid 1px #aeab98;
}

@media (min-width: 1200px) {
  .simplicity__statistics {
    margin-top: 100px;
    padding-top: 50px;
  }
}

@media (min-width: 1560px) {
  .simplicity__statistics {
    margin-top: 140px;
    padding-top: 70px;
  }
}

.webpage--beige .simplicity__heading {
  font-family: "Cinzel", "Georgia", serif;
}

@media (min-width: 1200px) {
  .webpage--beige .simplicity__heading {
    font-size: 60px;
    line-height: 1;
    letter-spacing: -0.025em;
  }
}

.webpage--beige .simplicity__content {
  padding-top: 60px;
}

@media (min-width: 768px) {
  .webpage--beige .simplicity__content {
    padding-top: 0;
  }
}

.webpage--modern .simplicity__preheading {
  color: #fefae9;
}

.webpage--modern .simplicity__heading {
  font-weight: 700;
}

/* 4.13 Approach */
.approach {
  padding: 60px 0;
  background-color: #f8f5f4;
  counter-reset: approach;
}

@media (min-width: 1200px) {
  .approach {
    padding: 141px 0 185px;
  }
}

.approach--technical {
  background-color: transparent;
}

.approach--technical .approach__card {
  background-color: transparent;
  border: solid 1px #000000;
}

.approach--technical .approach__card-icon svg {
  fill: #000000;
}

.approach--technical .approach__card-heading {
  font-weight: 400;
}

@media (min-width: 992px) {
  .approach--technical .approach__header {
    margin-bottom: 60px;
  }
}

@media (min-width: 1200px) {
  .approach--technical .approach__header {
    margin-bottom: 90px;
  }
}

.approach--technical .approach__heading + * {
  margin-top: 20px;
}

.approach .col-12 {
  margin-bottom: 32px;
}

@media (min-width: 992px) {
  .approach .col-12 {
    margin-bottom: 0;
  }
}

.approach__header {
  margin-bottom: 30px;
}

@media (min-width: 1200px) {
  .approach__header {
    margin-bottom: 71px;
  }
}

.approach__heading {
  font-weight: 400;
}

.approach__heading + * {
  margin-top: 40px;
}

.approach__text {
  font-size: 16px;
  line-height: 1.7;
  color: #999999;
}

@media (min-width: 1200px) {
  .approach__text {
    font-size: 20px;
    margin-bottom: 91px;
  }
}

.approach__card {
  position: relative;
  padding: 30px;
  height: 100%;
  background-color: #ffffff;
  counter-increment: approach;
}

@media (min-width: 576px) {
  .approach__card {
    padding: 40px;
  }
}

@media (min-width: 1200px) {
  .approach__card {
    padding: 55px 50px 50px 55px;
  }
}

.approach__card::before {
  content: "0" counter(approach);
  position: absolute;
  right: 30px;
  top: 30px;
  font-size: 16px;
  line-height: 1;
  color: #999999;
  letter-spacing: 0.8px;
}

@media (min-width: 576px) {
  .approach__card::before {
    right: 55px;
    top: 55px;
  }
}

.approach__card-icon {
  margin-bottom: 60px;
}

@media (min-width: 1200px) {
  .approach__card-icon {
    margin-bottom: 102px;
  }
}

.approach__card-icon svg {
  fill: #efb834;
}

.approach__card-heading {
  max-width: 210px;
  margin: 0 0 25px;
  font-size: 36px;
  line-height: 1.11;
  color: #000000;
  font-weight: 300;
}

.approach__card-text {
  font-size: 18px;
  line-height: 1.67;
  color: #999999;
}

.approach__statistics {
  margin-top: 20px;
  padding-top: 40px;
  border-top: solid 1px #999999;
}

@media (min-width: 992px) {
  .approach__statistics {
    margin-top: 120px;
    padding-top: 80px;
  }
}

.approach--parallax {
  background-image: url("../img/bg-our-story-2.jpg");
  background-repeat: no-repeat;
  background-size: cover;
  padding: 0;
}

.approach--parallax .approach__heading {
  font-weight: 600;
}

@media (max-height: 750px) {
  .approach--parallax .approach__heading {
    font-size: 32px;
    margin-bottom: 20px;
  }
}

@media (max-height: 750px) {
  .approach--parallax .approach__card {
    padding: 30px;
  }
}

@media (max-height: 750px) {
  .approach--parallax .approach__card-icon {
    margin-bottom: 25px;
  }
}

.approach--parallax .approach__card-heading {
  font-weight: 600;
}

@media (max-height: 750px) {
  .approach--parallax .approach__card-heading {
    font-size: 26px;
    margin: 0 0 18px;
  }
}

@media (max-height: 750px) {
  .approach--parallax .approach__card-text {
    font-size: 16px;
    line-height: 1.4;
  }
}

.webpage--beige .approach {
  background-color: transparent;
}

@media (min-width: 1200px) {
  .webpage--beige .approach {
    padding-bottom: 167px;
  }
}

.webpage--beige .approach__heading {
  font-family: "Cinzel", "Georgia", serif;
  color: #ffffff;
}

@media (min-width: 1200px) {
  .webpage--beige .approach__heading {
    font-size: 60px;
  }
}

.webpage--beige .approach__card {
  border: solid 1px #6a7067;
  background-color: #4d524b;
}

.webpage--beige .approach__card-icon svg {
  fill: #e6da89;
}

.webpage--beige .approach__card-heading {
  max-width: none;
  font-family: "Cinzel", "Georgia", serif;
  color: #ffffff;
}

/* 4.14 Reward */
.reward {
  padding: 32px 0;
}

@media (min-width: 768px) {
  .reward {
    display: flex;
  }
}

@media (min-width: 1200px) {
  .reward {
    padding: 54px 0;
  }
}

.reward__date {
  display: inline-block;
  vertical-align: top;
  margin-bottom: 20px;
  font-size: 16px;
  line-height: 1;
  color: #999999;
}

@media (min-width: 768px) {
  .reward__date {
    width: 36px;
    margin: 18px 40px 0 0;
  }
}

@media (min-width: 1200px) {
  .reward__date {
    margin: 12px 58px 0 0;
  }
}

.reward__logo {
  margin-bottom: 20px;
}

@media (min-width: 768px) {
  .reward__logo {
    width: 150px;
    margin: 10px 50px 0 0;
    flex-shrink: 0;
    text-align: center;
  }
}

@media (min-width: 1200px) {
  .reward__logo {
    margin: 5px 126px 0 0;
  }
}

.reward__logo img {
  max-width: 100%;
}

.reward__content {
  width: 100%;
}

@media (min-width: 768px) {
  .reward__content {
    width: auto;
    flex-grow: 1;
  }
}

@media (min-width: 992px) {
  .reward__project {
    display: flex;
  }
}

.reward__project + .reward__project {
  margin-top: 20px;
}

@media (min-width: 1200px) {
  .reward__project + .reward__project {
    margin-top: 32px;
  }
}

.reward__project-wrap {
  margin-bottom: 20px;
}

@media (min-width: 992px) {
  .reward__project-wrap {
    margin-bottom: 0;
    margin-right: auto;
  }
}

.reward__project-name {
  font-size: 24px;
  line-height: 1.33;
  color: #000000;
  letter-spacing: -0.025em;
}

@media (min-width: 1200px) {
  .reward__project-name {
    margin-bottom: 8px;
  }
}

.reward__project-name a {
  color: #000000;
}

.reward__project-name a:hover, .reward__project-name a:focus {
  outline: none;
  text-decoration: underline;
}

.reward__project-name a:active {
  opacity: 0.7;
}

.reward__project-text {
  font-size: 16px;
  line-height: 1.33;
  color: #999999;
}

.reward__project-link {
  display: inline-block;
  vertical-align: top;
  font-size: 14px;
  line-height: 1;
  color: #666666;
  letter-spacing: 0.025em;
  text-transform: uppercase;
  transition: color 0.3s ease;
}

@media (min-width: 992px) {
  .reward__project-link {
    margin-top: 8px;
    margin-left: 20px;
    flex-shrink: 0;
  }
}

.reward__project-link:hover, .reward__project-link:focus {
  outline: none;
  color: #efb834;
}

.reward__project-link:active {
  opacity: 0.7;
}

.webpage--modern .reward__project-link:hover, .webpage--modern .reward__project-link:focus {
  color: #57695a;
}

.webpage--beige .reward__project-name a {
  font-family: "Cinzel", "Georgia", serif;
  color: #ffffff;
}

.webpage--beige .reward__project-link {
  color: #999999;
}

.webpage--beige .reward__project-link:hover, .webpage--beige .reward__project-link:focus {
  color: #e6da89;
}

/* 4.15 Rewards */
.rewards {
  padding: 60px 0;
  background-color: #fefae9;
}

@media (min-width: 1200px) {
  .rewards {
    padding: 156px 0 118px;
  }
}

.rewards__preheading {
  margin-bottom: 12px;
  font-size: 16px;
  line-height: 1;
  color: #efb834;
  text-transform: uppercase;
}

.rewards__header {
  margin-bottom: 12px;
}

@media (min-width: 768px) {
  .rewards__header {
    margin-bottom: 30px;
  }
}

@media (min-width: 1200px) {
  .rewards__header {
    margin-bottom: 45px;
  }
}

.rewards__heading {
  font-weight: 400;
}

.rewards__heading + * {
  margin-top: 42px;
}

.rewards__text {
  font-size: 16px;
  line-height: 1.77;
  color: #999999;
}

@media (min-width: 1200px) {
  .rewards__text {
    margin-bottom: 50px;
    font-size: 18px;
  }
}

.rewards__list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.rewards__item + .rewards__item {
  border-top: solid 1px #dfdfdf;
}

.rewards + .bg-wrapper {
  margin-top: 26px;
}

.webpage--beige .rewards {
  background-color: transparent;
}

.webpage--beige .rewards__heading {
  font-family: "Cinzel", "Georgia", serif;
  color: #ffffff;
  text-transform: uppercase;
  letter-spacing: normal;
}

.webpage--beige .rewards .rewards__item + .rewards__item {
  border-top: solid 1px rgba(255, 255, 255, 0.2);
}

.webpage--modern .rewards__preheading {
  color: #57695a;
  text-align: center;
}

.webpage--modern .rewards__heading {
  text-align: center;
  font-weight: 700;
}

/* 4.16 Form */
.form {
  overflow: hidden;
}

.form__title {
  font-size: 48px;
  margin-bottom: 22px;
  letter-spacing: -1.2px;
}

.form__subtitle {
  font-size: 18px;
  color: #666666;
  margin-bottom: 100px;
}

.form__group {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 0;
}

@media (min-width: 768px) {
  .form__group {
    margin: 0 -30px 11px;
  }
}

.form__item {
  width: 100%;
  margin-bottom: 40px;
}

@media (min-width: 768px) {
  .form__item {
    width: calc(33.3333% - 60px);
    margin: 0 30px 50px;
  }
}

@media (min-width: 768px) {
  .form__field--width-full {
    width: 100%;
    margin: 0;
  }
}

.form__field--width-full {
  height: 150px;
  margin: 0;
}

@media (min-width: 1200px) {
  .form__field--width-full {
    height: 209px;
  }
}

.form__field--textarea {
  height: 150px;
}

@media (min-width: 1200px) {
  .form__field--textarea {
    height: 209px;
  }
}

.form__submit {
  width: 210px;
  padding: 20px;
  background-color: #f5480c;
  border: 1px solid #f5480c;
  font-size: 18px;
  color: #ffffff;
  text-transform: uppercase;
  margin-top: 40px;
  position: relative;
  left: 50%;
  transform: translateX(-50%);
  cursor: pointer;
  transition: all 0.3s ease;
}

@media (min-width: 768px) {
  .form__submit {
    left: 0;
    transform: none;
    margin-top: 95px;
  }
}

@media (min-width: 992px) {
  .form__submit:hover, .form__submit:focus {
    outline: none;
    background-color: transparent;
    color: #f5480c;
  }
}

/* 4.17 Timer */
.timer {
  display: flex;
}

.timer__digit:not(:last-child) {
  margin-right: 35px;
}

@media (min-width: 992px) {
  .timer__digit:not(:last-child) {
    margin-right: 54px;
  }
}

.timer__digit p {
  width: 30px;
  font-size: 30px;
  line-height: 30px;
  text-transform: uppercase;
  margin: 0 auto;
}

@media (min-width: 992px) {
  .timer__digit p {
    width: 85px;
    font-size: 72px;
    line-height: 72px;
  }
}

.timer__digit span {
  font-size: 14px;
  line-height: 24px;
  letter-spacing: 0.35px;
}

/* 4.18 Contact info */
.contact-info {
  list-style: none;
  padding: 0;
  margin: 0;
}

@media (min-width: 576px) {
  .contact-info {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }
}

.contact-info__item {
  display: flex;
  padding-top: 30px;
  margin-bottom: 12px;
}

.contact-info__item:last-child {
  margin-bottom: 0;
}

.contact-info__item:hover .contact-info__icon {
  border-color: #efb834;
}

.contact-info__item:hover .contact-info__icon svg {
  fill: #efb834;
}

@media (min-width: 576px) {
  .contact-info__item {
    width: calc(50% - 8px);
    margin-bottom: 0;
  }
  .contact-info__item:nth-child(n + 3) {
    margin-top: 24px;
  }
}

@media (min-width: 1200px) {
  .contact-info__item {
    width: calc(50% - 20px);
  }
}

.contact-info__icon {
  display: flex;
  justify-content: center;
  flex-shrink: 0;
  width: 60px;
  height: 60px;
  margin-right: 24px;
  margin-top: -24px;
  border: solid 1px #999999;
  border-radius: 50%;
  transition: border-color 0.3s ease;
}

@media (min-width: 1200px) {
  .contact-info__icon {
    width: 70px;
    height: 70px;
    margin-right: 38px;
    margin-top: -30px;
  }
}

.contact-info__icon svg {
  align-self: center;
  fill: #999999;
  transition: fill 0.3s ease;
}

.contact-info__title {
  margin-bottom: 20px;
  font-size: 14px;
  line-height: 1;
  color: #999999;
  text-transform: uppercase;
}

@media (min-width: 1200px) {
  .contact-info__title {
    margin-bottom: 25px;
  }
}

.contact-info__text {
  font-size: 16px;
  line-height: 1.66;
  color: #000000;
}

@media (min-width: 1200px) {
  .contact-info__text {
    font-size: 18px;
  }
}

.contact-info__text a {
  color: #000000;
  transition: color 0.3s ease;
}

.contact-info__text a:hover, .contact-info__text a:focus {
  outline: none;
  color: #efb834;
}

.contact-info__text a:active {
  opacity: 0.7;
}

.contact-info__text address {
  font-style: normal;
}

/* 4.19 Specialization */
@media (min-width: 576px) {
  .specialization {
    display: flex;
  }
}

.specialization__item {
  margin-bottom: 32px;
  padding: 0 10px;
  flex-grow: 1;
  /*	&-percent {
			position: absolute;
			left: 50%;
			top: 50%;
			transform: translateX(-50%) translateY(-50%);
		}*/
}

.specialization__item:last-child {
  margin-bottom: 0;
}

@media (min-width: 576px) {
  .specialization__item {
    margin-bottom: 0;
  }
}

.specialization__item-diagram {
  margin: 0 auto 24px;
  /*&::before {
				content: "";
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				border-radius: 50%;
				border: solid 1px $color-grey4;
			}

			svg {
				display: block;
				width: 100%;
				height: auto;
				box-sizing: border-box;
				stroke: $color-orange;
				stroke-width: 1px;
				fill: none;
				//stroke-dasharray: 100%;
			}*/
}

.specialization__item-text {
  max-width: 140px;
  margin: 0 auto;
  font-size: 20px;
  line-height: 1.5;
  color: #000000;
  text-align: center;
}

/* 4.20 Process step */
.process-step {
  list-style: none;
  padding: 0;
  margin: 0;
  counter-reset: process;
}

.process-step__item {
  position: relative;
  counter-increment: process;
  margin-bottom: 32px;
  padding-left: 50px;
  padding-bottom: 32px;
  border-bottom: solid 1px #e2e2e2;
}

@media (min-width: 1200px) {
  .process-step__item {
    margin-bottom: 63px;
    padding-left: 100px;
    padding-bottom: 51px;
  }
}

.process-step__item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.process-step__item::before {
  content: counter(process) ".";
  position: absolute;
  top: -4px;
  left: 0;
  font-size: 48px;
  line-height: 1;
  color: #cccccc;
  font-family: "Cinzel", "Georgia", serif;
  letter-spacing: -0.025em;
}

@media (min-width: 1200px) {
  .process-step__item::before {
    font-size: 80px;
  }
}

.process-step__item-title {
  margin-bottom: 20px;
  font-size: 20px;
  line-height: 1;
  color: #000000;
  letter-spacing: 0.05em;
  text-transform: uppercase;
}

@media (min-width: 1200px) {
  .process-step__item-title {
    font-size: 24px;
  }
}

.process-step__item-text {
  font-size: 16px;
  line-height: 1.66;
  color: #666666;
  letter-spacing: -0.015em;
}

@media (min-width: 1200px) {
  .process-step__item-text {
    font-size: 18px;
  }
}

/* 4.21 Diagram */
/*.preloader {
	display: none !important;
}*/
.diagram {
  position: relative;
  width: 160px;
  height: 160px;
}

.diagram__circle {
  display: block;
  fill: none;
  stroke: #dcdcdc;
  stroke-width: 1px;
}

.diagram__circle--progress {
  fill: none;
  stroke: #efb834;
  stroke-width: 1px;
  stroke-dasharray: 0 496;
  transition: all 2s linear;
}

.diagram__percent {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translateX(-50%) translateY(-50%);
  font-size: 36px;
  line-height: 1;
  color: #efb834;
}

/* 4.22 Our journal */
.our-journal--mb {
  margin-bottom: 60px;
}

@media (min-width: 768px) {
  .our-journal--mb {
    margin-bottom: 100px;
  }
}

@media (min-width: 992px) {
  .our-journal--mb {
    margin-bottom: 140px;
  }
}

@media (min-width: 1200px) {
  .our-journal--mb {
    margin-bottom: 196px;
  }
}

.our-journal__header {
  text-align: center;
  margin-bottom: 16px;
}

@media (min-width: 576px) {
  .our-journal__header {
    margin-bottom: 24px;
  }
}

@media (min-width: 768px) {
  .our-journal__header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 56px;
    text-align: left;
  }
}

@media (min-width: 992px) {
  .our-journal__header {
    margin-bottom: 70px;
  }
}

@media (min-width: 768px) {
  .our-journal__header--block {
    display: block;
  }
}

.our-journal__heading {
  margin-bottom: 16px;
  text-align: center;
}

@media (min-width: 768px) {
  .our-journal__heading {
    margin-bottom: 0;
    align-self: baseline;
  }
}

@media (min-width: 1200px) {
  .our-journal__heading {
    margin-bottom: 0px;
  }
}

.our-journal__text {
  margin-top: 16px;
  font-size: 16px;
  line-height: 1.77;
  color: #999999;
  text-align: center;
}

@media (min-width: 1200px) {
  .our-journal__text {
    margin-top: 54px;
  }
}

@media (min-width: 768px) {
  .our-journal__more {
    align-self: baseline;
  }
}

.our-journal__list {
  list-style: none;
  padding: 0;
}

.our-journal__list .news-card {
  padding-left: 0;
  padding-right: 0;
}

@media (min-width: 768px) {
  .our-journal__list .news-card {
    padding-left: 15px;
    padding-right: 15px;
  }
}

.webpage--beige .our-journal__heading {
  font-family: "Cinzel", "Georgia", serif;
  color: #ffffff;
}

@media (min-width: 768px) {
  .webpage--beige .our-journal__heading {
    text-align: left;
  }
}

.webpage--beige .our-journal__text {
  color: #999999;
}

@media (min-width: 768px) {
  .webpage--beige .our-journal__text {
    text-align: left;
  }
}

/* 4.23 Contact block */
.contact-block {
  max-width: 360px;
}

.contact-block--thumb {
  background-image: linear-gradient(to top, rgba(0, 0, 0, 0.6), transparent);
}

.contact-block__hint {
  font-size: 14px;
  line-height: 1;
  color: #999999;
  text-transform: uppercase;
}

.contact-block__title {
  margin-bottom: 12px;
  font-size: 30px;
  line-height: 1.6;
  color: #000000;
  letter-spacing: -0.025em;
  font-weight: 600;
}

.contact-block__title span {
  font-weight: 300;
  color: #999999;
}

.contact-block__address {
  margin-bottom: 12px;
  font-size: 16px;
  line-height: 1.625;
  color: #666666;
  font-style: normal;
}

@media (min-width: 1200px) {
  .contact-block__address {
    margin-bottom: 32px;
  }
}

.contact-block__email {
  margin-bottom: 24px;
}

@media (min-width: 1200px) {
  .contact-block__email {
    margin-bottom: 68px;
  }
}

.contact-block__email a {
  font-size: 16px;
  line-height: 1;
  color: #000000;
  transition: color 0.3s ease;
}

.contact-block__email a:hover, .contact-block__email a:focus {
  outline: none;
  color: #efb834;
}

.contact-block__email a:active {
  opacity: 0.7;
}

.contact-block__phone {
  margin-bottom: 24px;
}

.contact-block__phone span {
  display: block;
  margin-bottom: 12px;
}

@media (min-width: 1200px) {
  .contact-block__phone {
    margin-bottom: 56px;
  }
  .contact-block__phone span {
    margin-bottom: 22px;
  }
}

.contact-block__phone a {
  font-size: 30px;
  line-height: 1;
  color: #000000;
  letter-spacing: -0.025em;
  transition: color 0.3s ease;
}

@media (min-width: 1200px) {
  .contact-block__phone a {
    font-size: 36px;
  }
}

.contact-block__phone a:hover, .contact-block__phone a:focus {
  outline: none;
  color: #efb834;
}

.contact-block__phone a:active {
  opacity: 0.7;
}

.contact-block__offices {
  margin-bottom: 36px;
}

.contact-block__offices span {
  display: block;
  margin-bottom: 12px;
}

@media (min-width: 1200px) {
  .contact-block__offices {
    margin-bottom: 64px;
  }
  .contact-block__offices span {
    margin-bottom: 26px;
  }
}

.contact-block__offices-list {
  display: flex;
  flex-wrap: wrap;
}

.contact-block__offices-item {
  position: relative;
  margin-right: 20px;
  margin-bottom: 8px;
  font-size: 16px;
  line-height: 1;
  color: #000000;
}

@media (min-width: 1200px) {
  .contact-block__offices-item {
    margin-right: 24px;
    margin-bottom: 12px;
  }
}

.contact-block__offices-item::after {
  content: "|";
  position: absolute;
  right: -12px;
  color: #999999;
}

.contact-block__offices-item:last-child {
  margin-right: 0;
}

.contact-block__offices-item:last-child::after {
  display: none;
}

.contact-block__offices-item:hover, .contact-block__offices-item:focus {
  outline: none;
  color: #000000;
  text-decoration: underline;
}

.contact-block__offices-item:active {
  opacity: 0.7;
}

.contact-block__offices-item--active {
  cursor: default;
  pointer-events: none;
  color: #efb834;
}

/* 4.24 Feedback form */
.feedback-form__title {
  font-size: 54px;
  font-weight: bold;
  letter-spacing: -1.35px;
  text-transform: capitalize;
  margin-bottom: 27px;
}

@media (max-height: 800px) {
  .feedback-form__title {
    font-size: 30px;
    margin-bottom: 15px;
  }
}

.feedback-form__field {
  margin-bottom: 15px;
}

.feedback-form__btn {
  margin-top: 40px;
  border-radius: 0;
  text-transform: capitalize;
}

/* 4.25 Header toggle */
.header-toggle {
  width: 48px;
  height: 48px;
  padding: 0;
  border: solid 1px #e2e2e2;
  border-left: none;
  background-color: #ffffff;
  cursor: pointer;
  position: relative;
}

.header-toggle:hover, .header-toggle:focus {
  outline: none;
}

.header-toggle::before, .header-toggle::after {
  content: "";
  position: absolute;
  left: 8px;
  right: 8px;
  top: 50%;
  height: 4px;
  background-color: #000000;
  border-radius: 2px;
  transition: transform 0.3s ease;
}

.header-toggle::before {
  transform: translateY(-175%);
}

.header-toggle::after {
  transform: translateY(75%);
}

.header-toggle.on::before {
  animation-name: before;
  animation-duration: 0.5s;
  animation-fill-mode: forwards;
}

.header-toggle.on::after {
  animation-name: after;
  animation-duration: 0.5s;
  animation-fill-mode: forwards;
}

.header-toggle.off::before {
  animation-name: before-back;
  animation-duration: 0.5s;
  animation-fill-mode: forwards;
}

.header-toggle.off::after {
  animation-name: after-back;
  animation-duration: 0.5s;
  animation-fill-mode: forwards;
}

@keyframes before {
  0% {
    transform: translateY(-175%);
  }
  50% {
    transform: translateY(-50%);
  }
  100% {
    transform: translateY(-50%) rotate(45deg);
  }
}

@keyframes after {
  0% {
    transform: translateY(75%);
  }
  50% {
    transform: translateY(-50%);
  }
  100% {
    transform: translateY(-50%) rotate(-45deg);
  }
}

@keyframes before-back {
  0% {
    transform: translateY(-50%) rotate(45deg);
  }
  50% {
    transform: translateY(-50%);
  }
  100% {
    transform: translateY(-175%);
  }
}

@keyframes after-back {
  0% {
    transform: translateY(-50%) rotate(-45deg);
  }
  50% {
    transform: translateY(-50%);
  }
  100% {
    transform: translateY(75%);
  }
}

/* 4.26 Latest article */
.latest-article--featured {
  position: relative;
  display: flex;
}

.latest-article--featured .latest-article__content {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 24px;
  z-index: 1;
  padding: 0 32px;
}

@media (min-width: 992px) {
  .latest-article--featured .latest-article__content {
    bottom: 50px;
    padding: 0 70px;
  }
}

.latest-article--featured .latest-article__tag {
  position: absolute;
  top: 24px;
  left: 32px;
  font-size: 14px;
  line-height: 1;
  color: #ffffff;
}

@media (min-width: 992px) {
  .latest-article--featured .latest-article__tag {
    top: 60px;
    left: 70px;
    font-size: 18px;
  }
}

.latest-article--featured .latest-article__detail::before {
  color: #ffffff;
}

.latest-article--featured .latest-article__category a {
  color: #ffffff;
}

.latest-article--featured .latest-article__date {
  color: #ffffff;
}

@media (min-width: 992px) {
  .latest-article--featured .latest-article__heading {
    font-size: 36px !important;
  }
}

.latest-article--featured .latest-article__heading a {
  color: #ffffff;
}

.latest-article--featured .latest-article__image {
  align-self: stretch;
}

.latest-article__image {
  overflow: hidden;
}

.latest-article__image img {
  display: block;
  width: 100%;
  height: auto;
}

@media (min-width: 768px) {
  .latest-article__image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.latest-article__detail {
  display: flex;
  margin-bottom: 12px;
}

.latest-article__detail::before {
  content: "|";
  margin: 0 8px;
  font-size: 14px;
  line-height: 1;
  color: #999999;
}

.latest-article__category {
  font-size: 14px;
  line-height: 1;
  color: #000000;
  letter-spacing: 0.025em;
  text-transform: uppercase;
  order: -1;
}

.latest-article__category a {
  color: #000000;
  transition: color 0.3s ease;
}

.latest-article__category a:hover, .latest-article__category a:focus {
  outline: none;
  color: #57695a;
}

.latest-article__date {
  font-size: 14px;
  line-height: 1;
  color: #999999;
  letter-spacing: 0.025em;
}

.latest-article__heading {
  margin: 0;
  font-size: 24px;
  line-height: 1.16;
  color: #000000;
}

.latest-article__heading a {
  color: #000000;
  transition: color 0.3s ease;
}

.latest-article__heading a:hover, .latest-article__heading a:focus {
  outline: none;
  color: #57695a;
}

.latest-article__heading a:active {
  opacity: 0.7;
}

@media (min-width: 768px) {
  .latest-article__heading {
    font-size: 18px;
  }
}

@media (min-width: 768px) {
  .latest-article__heading {
    font-size: 24px;
  }
}

/* 4.27 Latest articles */
.latest-articles__line {
  padding-bottom: 60px;
  border-bottom: solid 1px #dadada;
}

.latest-articles__header {
  margin: 0 0 20px;
}

@media (min-width: 768px) {
  .latest-articles__header {
    margin-bottom: 80px;
  }
}

.latest-articles__preheading {
  margin-bottom: 12px;
  font-size: 16px;
  line-height: 1;
  color: #efb834;
  text-transform: uppercase;
}

.latest-articles__featured-post {
  margin-bottom: 40px;
}

@media (min-width: 768px) {
  .latest-articles__featured-post {
    width: 100%;
    margin-bottom: 0;
  }
}

.latest-articles__list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.latest-articles__item {
  margin-top: 36px;
  padding-top: 36px;
  border-top: solid 1px #dadada;
}

.latest-articles__item:first-child {
  margin-top: 0;
  padding-top: 0;
  border-top: none;
}

@media (min-width: 768px) {
  .latest-articles__item {
    margin-top: 20px;
    padding-top: 20px;
  }
}

@media (min-width: 992px) {
  .latest-articles__item {
    margin-top: 36px;
    padding-top: 36px;
  }
}

.webpage--modern .latest-articles__preheading {
  text-align: center;
  color: #57695a;
}

.webpage--modern .latest-articles__heading {
  font-weight: 700;
  text-align: center;
}

/* 4.28 Fact */
.fact {
  max-width: 298px;
  padding-top: 24px;
  border-top: solid 1px #efb834;
}

@media (min-width: 768px) {
  .fact {
    padding-top: 32px;
  }
}

@media (min-width: 1200px) {
  .fact {
    padding-top: 40px;
  }
}

.fact--white .fact__title,
.fact--white .fact__value {
  color: #ffffff;
}

.fact__title {
  max-width: 120px;
  margin-bottom: 8px;
  font-size: 12px;
  line-height: 1.3;
  color: #000000;
  text-transform: uppercase;
}

@media (min-width: 768px) {
  .fact__title {
    margin-bottom: 12px;
    font-size: 16px;
  }
}

@media (min-width: 1200px) {
  .fact__title {
    font-size: 20px;
  }
}

.fact__value {
  font-size: 42px;
  line-height: 1;
  color: #000000;
}

@media (min-width: 768px) {
  .fact__value {
    font-size: 60px;
  }
}

@media (min-width: 1200px) {
  .fact__value {
    font-size: 72px;
  }
}

.webpage--modern .fact {
  border-top-color: #57695a;
}

/* 4.29 Hero banner */
.hero-banner {
  position: relative;
  background-color: #4d524b;
  height: 100vh;
  min-height: 480px;
}

@media (min-width: 768px) {
  .hero-banner {
    min-height: 540px;
  }
}

@media (min-width: 1200px) {
  .hero-banner {
    min-height: 600px;
  }
}

.hero-banner .swiper-container {
  margin: 0;
  padding-bottom: 50px;
}

@media (min-width: 576px) {
  .hero-banner .swiper-container {
    padding-bottom: 0;
  }
}

.hero-banner .swiper-container .swiper-pagination {
  position: absolute;
  bottom: 10px;
  right: 0;
  display: flex;
  justify-content: center;
}

@media (min-width: 576px) {
  .hero-banner .swiper-container .swiper-pagination {
    left: auto;
    bottom: 0;
    width: auto;
  }
}

.hero-banner .swiper-container .swiper-pagination .swiper-pagination-bullet {
  width: 8px;
  height: 8px;
  margin: 0 4px;
  appearance: none;
  border-radius: 4px;
  background-color: #ffffff;
  opacity: 1;
  transition: width 0.3s ease, background-color 0.3s ease;
}

@media (min-width: 576px) {
  .hero-banner .swiper-container .swiper-pagination .swiper-pagination-bullet {
    margin: 0 11px;
  }
}

.hero-banner .swiper-container .swiper-pagination .swiper-pagination-bullet-active {
  width: 36px;
  background-color: #efb834;
}

.hero-banner__inner {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
  padding: 0 15px;
}

@media (min-width: 1560px) {
  .hero-banner__inner {
    max-width: 1550px !important;
  }
}

.hero-banner__item {
  min-width: 100vw;
  max-width: 100%;
  display: flex;
}

@media (min-width: 576px) {
  .hero-banner__item {
    width: 510px;
    justify-content: center;
  }
}

@media (min-width: 768px) {
  .hero-banner__item {
    width: 690px;
  }
}

@media (min-width: 1200px) {
  .hero-banner__item {
    width: 1110px;
  }
}

@media (min-width: 1560px) {
  .hero-banner__item {
    width: 1520px;
  }
}

@media (min-width: 576px) {
  .hero-banner__item br {
    display: block;
  }
}

.hero-banner__item-slogan {
  max-width: 100% !important;
  width: 100% !important;
  height: auto !important;
  display: block !important;
  object-fit: contain !important;
}

.hero-banner__item-slogan {
  position: relative;
  top: -25vh;
}

.hero-banner__item-slogan-mobile {
  width: calc(100% - 100px) !important;
  height: auto !important;
  position: relative;
  top: -25vh;
  left: 20px;
}

@media (min-width: 576px) {
  .hero-banner__item-left {
    width: calc(100% - 240px);
  }
}

@media (min-width: 768px) {
  .hero-banner__item-left {
    width: calc(100% - 320px);
  }
}

@media (min-width: 1200px) {
  .hero-banner__item-left {
    width: calc(100% - 540px);
  }
}

.hero-banner__item-right {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  max-width: 540px;
  padding-top: 40px;
}

@media (min-width: 576px) {
  .hero-banner__item-right {
    width: 200px;
    padding-top: 0;
    margin-left: 40px;
  }
}

@media (min-width: 768px) {
  .hero-banner__item-right {
    width: 280px;
  }
}

@media (min-width: 1200px) {
  .hero-banner__item-right {
    width: 500px;
  }
}

.hero-banner__item-fact {
  width: calc(50% - 20px);
}

@media (min-width: 1200px) {
  .hero-banner__item-fact {
    width: calc(50% - 50px);
  }
}

.hero-banner__item-title {
  margin-bottom: 20px;
  font-size: 30px;
  line-height: 1.2;
  color: #fefae9;
  font-weight: 700;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

@media (min-width: 768px) {
  .hero-banner__item-title {
    font-size: 42px;
  }
}

@media (min-width: 1200px) {
  .hero-banner__item-title {
    font-size: 55px;
  }
}

@media (min-width: 1560px) {
  .hero-banner__item-title {
    margin-bottom: 6vh;
    font-size: 80px;
  }
}

.hero-banner__item-text {
  margin-bottom: 20px;
  font-size: 14px;
  line-height: 1.5;
  color: #fefae9;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.4);
}

@media (min-width: 768px) {
  .hero-banner__item-text {
    max-width: 490px;
    margin-bottom: 36px;
    font-size: 20px;
  }
}

@media (min-width: 1200px) {
  .hero-banner__item-text {
    max-width: 590px;
    font-size: 22px;
  }
}

@media (min-width: 1560px) {
  .hero-banner__item-text {
    margin-bottom: 10.3125vh;
    font-size: 24px;
  }
}

.hero-banner__down {
  position: absolute;
  left: 50%;
  bottom: -13px;
  transform: translateX(-50%);
  width: 120px;
  height: 40px;
  padding-top: 8px;
  text-align: center;
  border: none;
  background-color: transparent;
  background-size: 100% auto;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 275 103'%3e%3cpath fill-rule='evenodd' fill='%23FEFAE9' d='M.427 71.361s-8.822-8.972 51.994-8.972C93.315 62.389 83.361-.745 140.05 1c37.827 1.165 48.543 61.389 82.956 61.389 34.414 0 51.994 3.778 51.994 3.778V103H.427V71.361z'/%3e%3c/svg%3e");
  cursor: pointer;
}

@media (min-width: 1200px) {
  .hero-banner__down {
    width: 275px;
    height: 76px;
    padding-top: 24px;
  }
}

.hero-banner__down svg {
  width: 20px;
  height: auto;
  transition: fill 0.3s ease;
}

@media (min-width: 1200px) {
  .hero-banner__down svg {
    width: 26px;
  }
}

.hero-banner__down:hover, .hero-banner__down:focus {
  outline: none;
  fill: #efb834;
}

.simplicity {
  padding-top: 180px;
  overflow-x: hidden;
}

@media (min-width: 1200px) {
  .simplicity {
    padding-top: 180px;
  }
}

.simplicity .container {
  position: relative;
}

@media (max-width: 576px) {
  .simplicity .container {
    padding-left: 20px;
    padding-right: 20px;
  }
}

.simplicity .container::before {
  content: "";
  position: absolute;
  width: 120%;
  height: 100%;
  background-color: #2d3c2d;
}

.simplicity .container .row {
  padding: 20px;
}

.tparrows {
  width: 60px;
  height: 60px;
  border: solid 1px #ffffff;
  background-color: transparent;
  transition: all 0.3s ease;
}

.tparrows:hover, .tparrows:focus {
  border-color: #efb834;
  background-color: #efb834;
}

.tparrows::before {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  background-size: 36px auto;
  background-repeat: no-repeat;
  background-position: 50% 50%;
}

.tp-leftarrow.tparrows::before {
  content: "";
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 64 64'%3e%3cpath fill='%23ffffff' d='M21.1 32L47.2 4.5c1.1-1.1 1.1-2.7 0-3.7s-2.7-1.1-3.7 0L17.4 28.5c-1.9 1.9-1.9 5.1 0 6.9l26.1 27.7c.5.5 1.3.8 1.9.8.5 0 1.3-.3 1.9-.8 1.1-1.1 1.1-2.7 0-3.7L21.1 32z'/%3e%3c/svg%3e ");
}

.tp-rightarrow.tparrows::before {
  content: "";
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 64 64'%3e%3cpath fill='%23ffffff' d='M46.8 28.5L20.6.8c-1.1-1.1-2.7-1.1-3.7 0-1.1 1.1-1.1 2.7 0 3.7L42.8 32 16.6 59.5c-1.1 1.1-1.1 2.7 0 3.7.5.5 1.1.8 1.9.8s1.3-.3 1.9-.8l26.1-27.7c2.1-1.9 2.1-5.1.3-7z'/%3e%3c/svg%3e ");
}

.tp-bullet {
  width: 8px;
  height: 8px;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.tp-bullet.selected {
  background-color: #efb834;
}

#rev_slider_forcefullwidth + .simplicity {
  padding-top: 60px;
}

@media (min-width: 1200px) {
  #rev_slider_forcefullwidth + .simplicity {
    padding-top: 150px;
  }
}

/* Revolution Slider 100lvh height override */
.rev_slider_wrapper.fullscreen-container,
.rev_slider.fullscreenbanner {
  height: 100lvh !important;
  min-height: 100lvh !important;
}

.hero-banner {
  height: 100lvh;
}
/* End */

.webpage--modern .hero-banner .swiper-container .swiper-pagination .swiper-pagination-bullet-active {
  background-color: #57695a;
}

.webpage--modern .hero-banner__down:hover, .webpage--modern .hero-banner__down:focus {
  fill: #57695a;
}

.webpage--modern .tp-bullet.selected {
  background-color: #57695a;
}

.webpage--modern .tparrows:hover, .webpage--modern .tparrows:focus {
  border-color: #57695a;
  background-color: #57695a;
}

/* 預設：桌機顯示，手機隱藏 */
.hero-banner--desktop {
  display: block;
}
.hero-banner--mobile {
  display: none;
}

/* Override Revolution Slider tp-parallax-wrap left positioning */
.tp-parallax-wrap {
  left: 0 !important;
}

/* 小於 576px 時：手機顯示，桌機隱藏 */
@media (max-width: 576px) {
  .hero-banner--desktop {
    display: none;
  }
  .hero-banner--mobile {
    display: block;
  }
}

/* 4.30 Instagram block */
.instagram-block__title {
  margin: 0 0 32px;
  font-size: 24px;
  line-height: 1;
  color: #000000;
  font-weight: 700;
  display: flex;
  justify-content: center;
  align-items: center;
}

@media (min-width: 768px) {
  .instagram-block__title {
    margin-bottom: 64px;
  }
}

.instagram-block__icon {
  margin-right: 16px;
}

@media (min-width: 576px) {
  .instagram-block__list {
    display: flex;
    flex-wrap: wrap;
  }
}

.instagram-block a {
  position: relative;
  display: block;
}

@media (min-width: 576px) {
  .instagram-block a {
    width: 50%;
  }
}

@media (min-width: 768px) {
  .instagram-block a {
    width: 33.33%;
  }
}

@media (min-width: 992px) {
  .instagram-block a {
    width: 25%;
  }
}

@media (min-width: 1200px) {
  .instagram-block a {
    width: 20%;
  }
}

.instagram-block a::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-repeat: no-repeat;
  background-size: 32px 32px;
  background-color: rgba(0, 0, 0, 0.3);
  background-position: 50% 50%;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 64 64'%3e%3cpath fill='%23ffffff' d='M32 16.9c-8.3 0-15 6.8-15 15.1s6.7 15.1 15 15.1S47 40.3 47 32s-6.7-15.1-15-15.1zm0 25.2c-5.5 0-10-4.5-10-10.1s4.5-10.1 10-10.1S42 26.4 42 32s-4.5 10.1-10 10.1zM47 10c-2.8 0-5 2.3-5 5s2.2 5 5 5 5-2.3 5-5-2.2-5-5-5z'/%3e%3cpath fill='%23ffffff' d='M48 0H16C7.2 0 0 7.2 0 16v31.7C0 56.7 7.3 64 16.3 64h31.5C56.7 64 64 56.7 64 47.7V16c0-8.8-7.2-16-16-16zm11 47.7C59 53.9 54 59 47.8 59H16.3C10.1 59 5 54 5 47.8V16C5 9.9 9.9 5 16 5h32c6.1 0 11 4.9 11 11v31.7z'/%3e%3c/svg%3e");
  opacity: 0;
  transition: opacity 0.3s ease;
}

.instagram-block a:hover, .instagram-block a:focus {
  outline: none;
}

.instagram-block a:hover::before, .instagram-block a:focus::before {
  opacity: 1;
}

.instagram-block a img {
  display: block;
  width: 100%;
  height: auto;
}

/* 4.31 Layout */
.layout--left-aside {
  max-width: 1920px;
  padding-top: 30px;
}

@media (min-width: 1200px) {
  .layout--left-aside {
    padding-top: 40px;
    padding-left: 300px;
  }
}

@media (min-width: 1560px) {
  .layout--left-aside {
    padding-left: 375px;
  }
}

/* 4.32 Sidebar */
.sidebar {
  width: 100%;
  margin-bottom: 50px;
}

@media (min-width: 992px) {
  .sidebar {
    max-width: 300px;
    margin-bottom: 0;
  }
}

.sidebar__block + .sidebar__block {
  margin-top: 40px;
}

@media (min-width: 992px) {
  .sidebar__block + .sidebar__block {
    margin-top: 68px;
  }
}

.sidebar__block > p {
  font-size: 20px;
  line-height: 26px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  text-align: center;
  margin: 0;
  margin-bottom: 10px;
}

@media (min-width: 992px) {
  .sidebar__block > p {
    text-align: left;
  }
}

.sidebar__block-title {
  margin-bottom: 32px;
  font-size: 30px;
  line-height: 1;
  color: #000000;
  letter-spacing: -0.015em;
}

.sidebar__block input[type="search"] {
  width: 100%;
  max-width: 300px;
  position: relative;
  left: 50%;
  transform: translateX(-50%);
}

@media (min-width: 992px) {
  .sidebar__block input[type="search"] {
    left: 0;
    transform: none;
  }
}

.sidebar__social {
  margin-top: 23px;
  justify-content: center;
}

@media (min-width: 992px) {
  .sidebar__social {
    justify-content: flex-start;
  }
}

.sidebar__popular-posts-wrapper {
  margin-top: 29px;
  margin-bottom: 57px;
}

.sidebar__categories {
  margin-top: 31px;
}

.sidebar__categories-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  padding-bottom: 13px;
}

@media (min-width: 992px) {
  .sidebar__categories-item:hover .sidebar__categories-name,
  .sidebar__categories-item:hover .sidebar__categories-num {
    color: #efb834;
  }
}

.sidebar__categories-item + .sidebar__categories-item {
  border-top: 1px solid #dadada;
  padding-top: 13px;
}

.sidebar__categories-item.active .sidebar__categories-name,
.sidebar__categories-item.active .sidebar__categories-num {
  color: #efb834;
}

.sidebar__categories-name, .sidebar__categories-num {
  font-size: 14px;
  text-transform: uppercase;
  transition: color 0.3s ease;
}

.sidebar__categories-num {
  color: #999999;
}

.sidebar__ads {
  display: block;
  max-width: 300px;
  height: 375px;
  margin: 0 auto;
  margin-top: 43px;
}

@media (min-width: 992px) {
  .sidebar__ads {
    margin: 43px 0 0;
  }
}

.sidebar__ads img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.sidebar__testimonial {
  margin: 0 auto;
}

.sidebar__services {
  list-style: none;
  padding: 0;
  margin: 0;
  counter-reset: services;
}

.sidebar__services-item {
  counter-increment: services;
  margin-bottom: -1px;
  padding: 18px 20px 14px 0;
  border-bottom: solid 1px #dadada;
  font-size: 18px;
  line-height: 1.33;
  color: #000000;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.sidebar__services-item:last-child {
  border-bottom: none;
}

.sidebar__services-item a {
  display: flex;
  align-items: center;
  color: #000000;
  transition: color 0.3s ease;
}

.sidebar__services-item a::before {
  content: counter(services);
  width: 35px;
  margin-right: 19px;
  flex-shrink: 0;
  font-size: 30px;
  line-height: 1.33;
  color: #cccccc;
  border-right: solid 1px #dfdfdf;
  transition: color 0.3s ease;
}

.sidebar__services-item a:hover, .sidebar__services-item a:focus {
  outline: none;
  color: #efb834;
}

.sidebar__services-item a:hover::before, .sidebar__services-item a:focus::before {
  color: #000000;
}

.sidebar__services-item a:active {
  opacity: 0.7;
}

.popular-post {
  display: block;
  padding-bottom: 14px;
}

@media (min-width: 992px) {
  .popular-post:hover .title {
    color: #efb834;
  }
  .popular-post:hover .popular-post__num {
    color: #000000;
  }
}

.popular-post + .popular-post {
  border-top: 1px solid #dadada;
  padding-top: 19px;
}

.popular-post figure {
  display: flex;
  align-items: center;
  margin: 0;
}

.popular-post__num {
  width: 35px;
  font-size: 30px;
  line-height: 26px;
  text-transform: uppercase;
  color: #cccccc;
  transition: color 0.3s ease;
}

.popular-post__content {
  border-left: 1px solid #dfdfdf;
  padding-left: 19px;
}

.popular-post__content p {
  margin: 0;
}

.popular-post__content .tag {
  font-size: 12px;
  color: #999999;
  text-transform: uppercase;
}

.popular-post__content .title {
  font-size: 18px;
  color: #000000;
  transition: color 0.3s ease;
}

.newsletter {
  padding: 39px 30px 55px;
  background-color: #f9f5f4;
  margin-bottom: 63px;
}

.newsletter__title {
  font-size: 20px;
  line-height: 26px;
  text-transform: uppercase;
  margin-bottom: 14px;
}

.newsletter__text {
  font-size: 14px;
  line-height: 26px;
  color: #666666;
  margin-bottom: 35px;
}

.newsletter input {
  width: 100%;
  height: 50px;
  padding: 18px;
  background-color: #ffffff;
}

.newsletter__btn {
  width: 100%;
  height: 50px;
  border: none;
  margin-top: 14px;
  font-size: 16px;
  line-height: 26px;
  color: #ffffff;
  text-transform: uppercase;
  letter-spacing: 0.8px;
  background-color: #f5480c;
  cursor: pointer;
  transition: all 0.3s ease;
}

@media (min-width: 992px) {
  .newsletter__btn:hover {
    opacity: 0.8;
  }
}

/* 4.33 Menu */
.menu {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 1000;
  width: 100%;
  height: 100%;
  min-height: 480px;
  overflow: auto;
  background-color: #2d3c2d;
  background-repeat: no-repeat;
  background-position: right 0 bottom 0;
  background-size: auto 100%;
  transform: translateX(100%);
  transition: transform 0.3s ease;
}

@media (min-width: 576px) {
  .menu {
    width: 56%;
  }
}

@media (min-width: 992px) {
  .menu {
    max-width: 750px;
  }
}

.menu--opened {
  transform: translateX(0);
}

.menu__inner {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 20px 30px;
}

@media (min-width: 768px) {
  .menu__inner {
    padding: 44px 60px;
  }
}

@media (min-width: 992px) {
  .menu__inner {
    padding: 80px 100px;
  }
}

.menu__wrapper {
  margin-top: 50px;
  margin-bottom: 20px;
  padding: 6px 0;
  overflow: auto;
}

@media (min-width: 992px) {
  .menu__wrapper {
    margin-top: 10.7vh;
  }
}

.menu__bottom {
  margin-top: auto;
}

.menu__copy {
  margin-bottom: 16px;
  font-size: 14px;
  line-height: 1.71;
  color: #fefae9;
  letter-spacing: 0.015em;
}

@media (min-width: 992px) {
  .menu__copy {
    margin-bottom: 4.44vh;
  }
}

.menu__copy span {
  color: #fefae9;
}

.menu__dropdown {
  display: none;
}

.menu__list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.menu__sublist {
  list-style: none;
  padding: 0;
  margin: 0;
}

.menu__subitem {
  display: block;
  margin-top: 16px;
  line-height: 1 !important;
}

.menu__subitem:first-child {
  margin-top: 20px;
}

@media (min-width: 992px) {
  .menu__subitem:first-child {
    margin-top: 32px;
  }
}

.menu__sublink {
  display: inline-block;
  vertical-align: top;
  font-size: 16px;
  line-height: 1;
  color: #999999;
  transition: color 0.3s ease;
}

.menu__sublink--current {
  color: #000000;
  pointer-events: none;
}

.menu__sublink--current:hover, .menu__sublink--current:focus {
  color: #000000 !important;
}

.menu__sublink:hover, .menu__sublink:focus {
  outline: none;
  color: #000000 !important;
}

.menu__sublink:active {
  opacity: 0.7;
}

.menu__item {
  margin-bottom: 20px;
}

@media (min-width: 992px) {
  .menu__item {
    margin-bottom: 32px;
  }
}

.menu__item:last-child {
  margin-bottom: 0;
}

.menu__item--current .menu__link {
  color: #fefae9;
}

.menu__link {
  font-size: 24px;
  line-height: 1;
  color: #fefae9;
  font-weight: 600;
  transition: color 0.3s ease;
}

@media (min-width: 992px) {
  .menu__link {
    font-size: 36px;
  }
}

.menu__link:hover, .menu__link:focus {
  outline: none;
  color: #efb834;
}

.menu__link:active {
  opacity: 0.7;
}

.menu__close {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  justify-content: center;
  width: 48px;
  height: 48px;
  padding: 0;
  border: none;
  background-color: transparent;
  appearance: none;
  cursor: pointer;
}

@media (min-width: 768px) {
  .menu__close {
    top: 32px;
    right: 48px;
  }
}

@media (min-width: 768px) {
  .menu__close {
    top: 68px;
    right: 88px;
  }
}

.menu__close svg {
  fill: #fefae9;
  align-self: center;
  transition: fill 0.3s ease;
}

.menu__close:hover, .menu__close:focus {
  outline: none;
}

.menu__close:hover svg, .menu__close:focus svg {
  fill: #efb834;
}

.menu__close:active {
  opacity: 0.7;
}

.webpage--beige .menu {
  background-color: #4d524b;
}

.webpage--beige .menu__item:last-child {
  margin-bottom: 0;
}

.webpage--beige .menu__item--current > a {
  color: #e6da89;
}

.webpage--beige .menu__link:hover, .webpage--beige .menu__link:focus, .webpage--beige .menu__sublink:hover, .webpage--beige .menu__sublink:focus {
  outline: none;
  color: #e6da89 !important;
}

.webpage--beige .menu__sublink--current {
  color: #e6da89 !important;
}

.webpage--beige .menu__copy {
  color: #999999;
}

.webpage--beige .menu__copy span {
  color: #ffffff;
}

.webpage--beige .menu__close svg {
  fill: #ffffff;
}

.webpage--beige .menu__close:hover, .webpage--beige .menu__close:focus {
  outline: none;
}

.webpage--beige .menu__close:hover svg, .webpage--beige .menu__close:focus svg {
  fill: #e6da89;
}

/* 4.34 Latest projects */
.latest-projects {
  padding-bottom: 24px;
}

@media (min-width: 576px) {
  .latest-projects {
    padding-bottom: 48px;
  }
}

@media (min-width: 1200px) {
  .latest-projects {
    padding-bottom: 104px;
  }
}

.latest-projects__header {
  text-align: center;
  margin-bottom: 16px;
}

@media (min-width: 576px) {
  .latest-projects__header {
    margin-bottom: 24px;
  }
}

@media (min-width: 768px) {
  .latest-projects__header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 56px;
    text-align: left;
  }
}

@media (min-width: 992px) {
  .latest-projects__header {
    margin-bottom: 80px;
  }
}

@media (min-width: 1200px) {
  .latest-projects__header {
    margin-bottom: 105px;
  }
}

.latest-projects__preheading {
  margin-bottom: 12px;
  font-size: 16px;
  line-height: 1;
  color: #efb834;
  text-transform: uppercase;
}

.latest-projects__heading {
  margin-bottom: 16px;
  text-align: center;
}

@media (min-width: 768px) {
  .latest-projects__heading {
    margin-bottom: 0;
    align-self: baseline;
  }
}

@media (min-width: 1200px) {
  .latest-projects__heading {
    margin-bottom: 0px;
  }
}

@media (min-width: 768px) {
  .latest-projects__more {
    align-self: baseline;
  }
}

.latest-projects__inner {
  position: relative;
}

@media (min-width: 1560px) {
  .latest-projects__inner {
    max-width: 1550px;
  }
}

@media (min-width: 768px) {
  .latest-projects__slider-thumbs {
    position: absolute;
    left: 15px;
    right: 15px;
    bottom: 0;
  }
}

.latest-projects__slider-thumbs .swiper-wrapper {
  align-items: flex-end;
}

.webpage--modern .latest-projects__inner {
  max-width: 1550px;
}

.webpage--modern .latest-projects__preheading {
  color: #57695a;
  text-align: center;
}

.webpage--modern .latest-projects__header {
  display: block;
}

.webpage--modern .latest-projects__heading {
  font-weight: 700;
}

.webpage--modern .latest-projects .swiper-pagination .swiper-pagination-bullet {
  opacity: 1;
}

@media (min-width: 1200px) {
  .webpage--modern .latest-projects .swiper-pagination {
    margin-top: 90px !important;
  }
}

@media (min-width: 768px) {
  .th-latest-projects__inner {
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
  }
}

.th-latest-projects__header {
  padding: 40px 0;
  background-repeat: no-repeat;
  background-position: 50% 0;
  background-size: cover;
  background-image: url("../img/th-latest-bg.jpg");
}

@media (min-width: 1200px) {
  .th-latest-projects__header {
    padding: 140px 0;
  }
}

.th-latest-projects__header-nav {
  display: flex;
  margin-top: 24px;
}

@media (min-width: 768px) {
  .th-latest-projects__header-nav {
    margin-top: 0;
    margin-bottom: 8px;
    margin-left: 20px;
  }
}

.th-latest-projects__heading {
  margin-bottom: 24px;
  font-weight: 400;
}

.th-latest-projects__btn {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 60px;
  height: 60px;
  border: solid 1px #000000;
  background-color: transparent;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.th-latest-projects__btn:hover, .th-latest-projects__btn:focus {
  outline: none;
  background-color: #ffdf91;
}

.th-latest-projects__btn:active {
  opacity: 0.7;
}

.th-latest-projects__btn + .th-latest-projects__btn {
  margin-left: 9px;
}

.th-latest-projects__carousel {
  width: calc(100% + 8px);
  margin-left: -4px;
  margin-bottom: -4px;
}

/* 4.35 Review */
.review {
  overflow: hidden;
}

@media (min-width: 1200px) {
  .review__content {
    margin-bottom: 60px;
  }
}

.review__content::before {
  content: "";
  display: block;
  width: 50px;
  height: 38px;
  margin-bottom: 16px;
  background-repeat: no-repeat;
  background-position: 50% 0;
  background-size: 100% auto;
  background-image: url("../img/quotation.png");
}

@media (min-width: 992px) {
  .review__content::before {
    width: 64px;
    height: 48px;
    margin-bottom: 24px;
  }
}

@media (min-width: 1200px) {
  .review__content::before {
    width: 78px;
    height: 58px;
    margin-bottom: 46px;
  }
}

.review__image {
  position: relative;
  background-repeat: no-repeat;
  background-size: contain;
  background-image: url("../img/review-shapes.png");
  background-position: center;
  min-height: 300px;
}

@media (min-width: 450px) {
  .review__image {
    min-height: 400px;
  }
}

.review__image-author {
  position: absolute;
  border-radius: 50%;
  overflow: hidden;
}

.review__image-author img {
  display: block;
  width: 100%;
  height: auto;
}

.review__image-author--one {
  top: 0.2%;
  left: 58.2%;
  width: 28.94%;
}

.review__image-author--two {
  top: 45.5%;
  left: 67%;
  width: 17.89%;
}

.review__image-author--three {
  top: 45.5%;
  left: 30.4%;
  width: 21.93%;
}

.review__image-author--four {
  top: 30%;
  left: 5%;
  width: 14.73%;
}

.review__image-author--five {
  top: 72.4%;
  left: 17%;
  width: 13.15%;
}

.review__image-author--six {
  top: 78.5%;
  right: 33%;
  width: 10.52%;
}

@media (min-width: 1200px) {
  .review__image-author--six {
    top: 69.5%;
    right: 0;
  }
}

.review__image img {
  max-width: 100%;
  height: auto;
}

@media (min-width: 1200px) {
  .review__image img {
    max-width: none;
  }
}

.review__text {
  margin-bottom: 24px;
  font-size: 16px;
  line-height: 1.53;
  color: #000000;
  font-weight: 300;
}

@media (min-width: 992px) {
  .review__text {
    margin-bottom: 36px;
    font-size: 24px;
  }
}

@media (min-width: 1200px) {
  .review__text {
    margin-bottom: 60px;
    padding-right: 10px;
    font-size: 30px;
  }
}

.review__author-name {
  margin-bottom: 8px;
  font-size: 14px;
  line-height: 1;
  color: #000000;
}

@media (min-width: 992px) {
  .review__author-name {
    margin-bottom: 12px;
    font-size: 18px;
  }
}

@media (min-width: 1200px) {
  .review__author-name {
    margin-bottom: 16px;
    font-size: 22px;
  }
}

.review__author-detail {
  font-size: 12px;
  line-height: 1;
  color: #999999;
}

@media (min-width: 992px) {
  .review__author-detail {
    font-size: 14px;
  }
}

.review__author-detail span {
  color: #000000;
}

.review--parallax {
  background-color: #ffffff;
}

.review--parallax .review__image {
  background-image: url("../img/review-shapes-2.png");
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center;
  height: 85vw;
}

@media (min-width: 768px) {
  .review--parallax .review__image {
    height: 35vw;
  }
}

@media (min-width: 1560px) {
  .review--parallax .review__image {
    transform: translateX(-90px);
  }
}

.review--parallax .review__content {
  padding-top: 41px;
}

.review--parallax .review__text {
  font-weight: 400;
}

.review--parallax .review__image-author--one {
  top: 2.2%;
  left: 47.2%;
  width: 24%;
}

.review--parallax .review__image-author--two {
  top: 47.5%;
  left: 54.4%;
  width: 14.6%;
}

.review--parallax .review__image-author--three {
  top: 47.5%;
  left: 24.8%;
  width: 17.93%;
}

.review--parallax .review__image-author--four {
  top: 32%;
  left: 4%;
  width: 12%;
}

.review--parallax .review__image-author--five {
  top: 74.4%;
  left: 13.6%;
  width: 11.1%;
}

.review--parallax .review__image-author--six {
  width: 8.52%;
  top: 71.5%;
  right: 16%;
}

/* 4.36 Partners */
.partners__line {
  padding-bottom: 60px;
  border-bottom: solid 1px #dddddd;
}

@media (min-width: 576px) {
  .partners__line {
    padding-bottom: 80px;
  }
}

@media (min-width: 992px) {
  .partners__line {
    padding-bottom: 120px;
  }
}

@media (min-width: 1200px) {
  .partners__line {
    padding-bottom: 173px;
  }
}

.partners__heading {
  margin-bottom: 16px;
  padding-bottom: 50px;
  text-align: center;
  color: #fefae9;
}

@media (min-width: 576px) {
  .partners__heading {
    margin-bottom: 20px;
  }
}

@media (min-width: 1200px) {
  .partners__heading {
    margin-bottom: 19px;
  }
}

.partners__heading--small {
  margin: 0 0 40px;
  font-size: 16px;
  line-height: 1.5;
  color: #000000;
  text-align: left;
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

@media (min-width: 576px) {
  .partners__heading--small {
    margin-bottom: 60px;
  }
}

@media (min-width: 1200px) {
  .partners__heading--small {
    margin-bottom: 87px;
  }
}

.partners__text {
  margin-bottom: 60px;
  font-size: 14px;
  line-height: 1.66;
  color: #666666;
  text-align: center;
}

@media (min-width: 576px) {
  .partners__text {
    margin-bottom: 80px;
    font-size: 16px;
  }
}

@media (min-width: 1200px) {
  .partners__text {
    margin-bottom: 118px;
    font-size: 18px;
  }
}

.partners__list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  margin-top: -64px;
}

.partners__list--justify-start {
  justify-content: flex-start;
}

.partners__item {
  width: 50%;
  text-align: center;
  align-self: center;
  margin-top: 64px;
}

@media (min-width: 576px) {
  .partners__item {
    width: 33.33%;
  }
}

@media (min-width: 992px) {
  .partners__item {
    width: 25%;
  }
}

@media (min-width: 1200px) {
  .partners__item {
    width: 20%;
  }
}

.partners__item img {
  display: inline-block;
  vertical-align: top;
  max-width: 90%;
  height: auto;
  filter: grayscale(1);
  transition: filter 0.3s ease;
}

.partners__item img:hover {
  filter: grayscale(0);
}

/* 4.37 Article list */
.article-list__header {
  text-align: center;
  margin-bottom: 16px;
}

@media (min-width: 576px) {
  .article-list__header {
    margin-bottom: 24px;
  }
}

@media (min-width: 768px) {
  .article-list__header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 56px;
    text-align: left;
  }
}

@media (min-width: 992px) {
  .article-list__header {
    margin-bottom: 80px;
  }
}

@media (min-width: 1200px) {
  .article-list__header {
    margin-bottom: 100px;
  }
}

.article-list__heading {
  margin-bottom: 16px;
  text-align: center;
}

@media (min-width: 768px) {
  .article-list__heading {
    margin-bottom: 0;
    align-self: baseline;
  }
}

@media (min-width: 1200px) {
  .article-list__heading {
    margin-bottom: 0px;
  }
}

@media (min-width: 768px) {
  .article-list__more {
    align-self: baseline;
  }
}

.article-list__list {
  list-style: none;
  padding: 0;
  margin: 0;
}

@media (min-width: 768px) {
  .article-list__list {
    margin: 0 -10px;
  }
}

@media (min-width: 1200px) {
  .article-list__list {
    margin: 0 -25px;
  }
}

.article-list__list + .article-list__list {
  margin-top: 24px;
}

@media (min-width: 768px) {
  .article-list__list + .article-list__list {
    margin-top: 50px;
  }
}

.article-list__item {
  margin-bottom: 24px;
  padding: 0;
}

@media (min-width: 768px) {
  .article-list__item {
    margin-bottom: 20px;
    padding: 0 10px;
  }
}

@media (min-width: 1200px) {
  .article-list__item {
    padding: 0 25px;
    margin-bottom: 50px;
  }
}

.article-list__item:last-child {
  margin-bottom: 0;
}

@media (min-width: 768px) {
  .article-list__item.col-md-12 .article-preview__image {
    height: 545px;
  }
}

/* 4.38 Our story */
.our-story {
  overflow: hidden;
}

.our-story__inner {
  padding-bottom: 60px;
  border-bottom: solid 1px #d6d6d6;
}

@media (min-width: 1200px) {
  .our-story__inner {
    padding-bottom: 104px;
  }
}

.our-story__intro {
  margin-bottom: 20px;
}

@media (min-width: 768px) {
  .our-story__intro {
    margin-bottom: 0;
  }
}

@media (min-width: 1200px) {
  .our-story__intro {
    padding-right: 100px;
  }
}

.our-story__intro-wrapper {
  position: absolute;
  bottom: 50px;
  left: 50%;
  transform: translateX(-50%);
}

@media (min-width: 992px) {
  .our-story__intro-wrapper {
    left: 14.1%;
    bottom: 16.9%;
    transform: translateX(0);
  }
}

.our-story__intro-text {
  width: 200px;
  font-size: 16px;
  line-height: 1.6;
  color: #ffffff;
}

@media (min-width: 992px) {
  .our-story__intro-text {
    width: 290px;
    font-size: 24px;
  }
}

@media (min-width: 1560px) {
  .our-story__intro-text {
    width: 360px;
    font-size: 30px;
  }
}

.our-story__image {
  position: relative;
}

@media (min-width: 1560px) {
  .our-story__image {
    width: 850px;
    margin-left: -240px;
  }
}

.our-story__image img {
  max-width: 100%;
  height: auto;
}

.our-story__exp {
  display: flex;
  margin-bottom: 12px;
  font-size: 16px;
  line-height: 1.33;
  color: #ffffff;
  font-weight: 600;
}

@media (min-width: 1560px) {
  .our-story__exp {
    margin-bottom: 25px;
  }
}

.our-story__exp-value {
  margin-right: 10px;
  font-size: 72px;
  line-height: 1;
  font-weight: 300;
  letter-spacing: -0.025em;
  align-self: center;
}

@media (min-width: 992px) {
  .our-story__exp-value {
    margin-right: 24px;
    font-size: 112px;
  }
}

@media (min-width: 1560px) {
  .our-story__exp-value {
    margin-right: 36px;
    font-size: 150px;
  }
}

.our-story__exp-text {
  max-width: 110px;
  text-transform: uppercase;
  align-self: center;
}

@media (min-width: 992px) {
  .our-story__exp-text {
    max-width: 178px;
    font-size: 24px;
  }
}

@media (min-width: 1560px) {
  .our-story__exp-text {
    max-width: 200px;
    font-size: 30px;
  }
}

.our-story__content {
  padding-bottom: 5px;
}

@media (min-width: 1560px) {
  .our-story__content {
    position: relative;
    left: 75px;
  }
}

.our-story__heading {
  margin-bottom: 20px;
}

@media (min-width: 1200px) {
  .our-story__heading {
    margin-bottom: 30px;
  }
}

@media (min-width: 1560px) {
  .our-story__heading {
    margin-bottom: 37px;
  }
}

.our-story__text {
  margin-bottom: 20px;
  font-size: 14px;
  line-height: 1.45;
  color: #666666;
}

@media (min-width: 1200px) {
  .our-story__text {
    max-width: 405px;
    margin-bottom: 24px;
    font-size: 18px;
  }
}

@media (min-width: 1560px) {
  .our-story__text {
    margin-bottom: 42px;
    line-height: 1.65;
  }
}

.our-story__text--style-bolditalic {
  font-size: 16px;
  color: #000000;
  font-style: italic;
}

@media (min-width: 1200px) {
  .our-story__text--style-bolditalic {
    font-size: 22px;
  }
}

@media (min-width: 1560px) {
  .our-story__text--style-bolditalic {
    line-height: 1.45;
  }
}

.our-story__buttons {
  display: flex;
  flex-wrap: wrap;
  margin-top: 30px;
}

@media (min-width: 1560px) {
  .our-story__buttons {
    margin-top: 60px;
    transform: translateY(10px);
  }
}

.our-story__btn {
  margin-right: 10px;
  align-self: flex-start;
}

@media (min-width: 1200px) {
  .our-story__btn {
    margin-right: 27px;
  }
}

.our-story__statistics {
  margin-top: 40px;
}

@media (min-width: 1200px) {
  .our-story__statistics {
    margin-top: 100px;
  }
}

@media (min-width: 1560px) {
  .our-story__statistics {
    margin-top: 130px;
  }
}

.our-story--parallax {
  background-image: url("../img/bg-our-story.jpg");
  background-repeat: no-repeat;
  background-size: cover;
  padding-bottom: 0 !important;
}

@media (min-width: 1200px) {
  .our-story--parallax {
    padding: 0 !important;
  }
}

@media (min-width: 1200px) {
  .our-story--parallax .scroll-wrap {
    padding: 0 !important;
  }
}

.our-story--parallax .row {
  height: 100%;
  align-items: center;
  justify-content: flex-start;
  flex-direction: column;
  margin-left: auto;
  margin-right: auto;
}

@media (min-width: 1200px) {
  .our-story--parallax .row {
    flex-direction: row;
    margin-left: -15px;
    margin-right: -15px;
  }
}

.our-story--parallax .our-story__inner {
  border-bottom: none;
  padding-bottom: 0;
}

@media (min-width: 1200px) {
  .our-story--parallax .our-story__inner {
    height: 100vh;
    padding: 0;
  }
}

.our-story--parallax .our-story__intro {
  height: 100%;
}

@media (min-width: 1200px) {
  .our-story--parallax .our-story__intro {
    padding-right: 50px;
    align-self: flex-end;
  }
}

@media (min-width: 1560px) {
  .our-story--parallax .our-story__intro {
    padding-right: 100px;
  }
}

.our-story--parallax .our-story__image {
  height: 100%;
  text-align: center;
  overflow: hidden;
}

@media (min-width: 1200px) {
  .our-story--parallax .our-story__image {
    margin-left: auto;
    width: 400px;
  }
}

@media (min-width: 1560px) {
  .our-story--parallax .our-story__image {
    margin-left: auto;
  }
}

@media (min-width: 1700px) {
  .our-story--parallax .our-story__image {
    margin-left: -74px;
    width: 706px;
  }
}

.our-story--parallax .our-story__image img {
  max-width: 50%;
  height: 100%;
  object-fit: contain;
  object-position: bottom;
}

@media (min-width: 576px) {
  .our-story--parallax .our-story__image img {
    max-width: 30%;
  }
}

@media (min-width: 1200px) {
  .our-story--parallax .our-story__image img {
    max-width: 100%;
    position: relative;
    bottom: -8px;
  }
}

@media (min-width: 1560px) {
  .our-story--parallax .our-story__image img {
    max-width: 100%;
  }
}

.our-story--parallax .our-story__content {
  text-align: center;
}

@media (min-width: 1200px) {
  .our-story--parallax .our-story__content {
    text-align: left;
    padding-bottom: 110px;
  }
}

@media (min-width: 1560px) {
  .our-story--parallax .our-story__content {
    transform: translateX(100px);
    padding-top: 24px;
  }
}

.our-story--parallax .our-story__heading {
  font-weight: 600;
}

.our-story--parallax .our-story__text span {
  font-size: 14px;
  font-weight: 600;
  color: #010101;
  font-style: normal;
}

.our-story--parallax .our-story__buttons {
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-top: 35px;
}

@media (min-width: 576px) {
  .our-story--parallax .our-story__buttons {
    flex-direction: row;
    align-items: flex-start;
  }
}

@media (min-width: 1200px) {
  .our-story--parallax .our-story__buttons {
    justify-content: flex-start;
    margin-top: 84px;
  }
}

.our-story--parallax .our-story__btn, .our-story--parallax .our-story__video-btn {
  align-self: center;
}

@media (min-width: 576px) {
  .our-story--parallax .our-story__btn, .our-story--parallax .our-story__video-btn {
    align-self: flex-start;
  }
}

.our-story--parallax .our-story__btn {
  background-color: #ffffff;
  border-color: #ffffff;
  color: #efb834;
  border-radius: 0;
  transition: all 0.3s ease;
  margin-bottom: 15px;
  margin-right: 0;
}

@media (min-width: 576px) {
  .our-story--parallax .our-story__btn {
    margin-bottom: 0;
    margin-right: 10px;
  }
}

.our-story--parallax .our-story__btn:hover, .our-story--parallax .our-story__btn:focus {
  background-color: #efb834;
  border-color: #efb834;
  color: #ffffff;
}

@media (min-width: 576px) {
  .th-our-story__inner {
    display: flex;
  }
}

.th-our-story__exp {
  padding: 40px;
  border-bottom: solid 1px #000000;
  background-image: url("../img/exp-bg.jpg");
  background-repeat: no-repeat;
  background-size: cover;
}

@media (min-width: 576px) {
  .th-our-story__exp {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    border-bottom: none;
    border-right: solid 1px #000000;
  }
}

@media (min-width: 768px) {
  .th-our-story__exp {
    width: 29.24%;
    flex-shrink: 0;
  }
}

@media (min-width: 1200px) {
  .th-our-story__exp {
    padding: 100px 80px;
  }
}

.th-our-story__exp-title {
  position: relative;
  max-width: 150px;
  margin-bottom: 56px;
  padding-top: 24px;
  font-size: 16px;
  line-height: 1.33;
  color: #000000;
  text-transform: uppercase;
}

@media (min-width: 1200px) {
  .th-our-story__exp-title {
    max-width: 170px;
    padding-top: 44px;
    font-size: 24px;
  }
}

.th-our-story__exp-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  display: block;
  width: 80px;
  border-top: solid 4px #000000;
}

.th-our-story__exp-value {
  font-size: 96px;
  line-height: 1;
  color: #000000;
}

@media (min-width: 1200px) {
  .th-our-story__exp-value {
    font-size: 130px;
  }
}

@media (min-width: 1560px) {
  .th-our-story__exp-value {
    font-size: 190px;
  }
}

.th-our-story__content {
  padding: 40px;
}

@media (min-width: 576px) {
  .th-our-story__content {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
}

@media (min-width: 1200px) {
  .th-our-story__content {
    padding: 100px 80px;
  }
}

.th-our-story__text {
  margin-bottom: 56px;
  font-size: 18px;
  line-height: 1.56;
  color: #000000;
}

@media (min-width: 768px) {
  .th-our-story__text {
    font-size: 24px;
  }
}

@media (min-width: 1200px) {
  .th-our-story__text {
    font-size: 42px;
  }
}

@media (min-width: 1560px) {
  .th-our-story__text {
    font-size: 46px;
  }
}

@media (min-width: 576px) {
  .th-our-story__buttons {
    margin-bottom: 12px;
  }
}

@media (min-width: 768px) {
  .th-our-story__buttons {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
  }
}

@media (min-width: 1200px) {
  .th-our-story__buttons {
    margin-bottom: 24px;
  }
}

.th-our-story__btn + * {
  margin-top: 16px;
}

@media (min-width: 768px) {
  .th-our-story__btn + * {
    margin-top: 0;
  }
}

@media (min-width: 768px) {
  .th-our-story__btn {
    margin-right: 28px;
  }
}

/* 4.39 Type service */
.type-service--lite {
  padding-top: 5px;
}

.type-service--lite .type-service__item {
  text-align: left;
}

.type-service--lite .type-service__item a {
  padding: 32px 72px 32px 32px;
  border: solid 1px #d6d6d6;
  transition: border-color 0.3s ease;
}

.type-service--lite .type-service__item a:hover, .type-service--lite .type-service__item a:focus {
  border-color: #efb834;
}

@media (min-width: 768px) {
  .type-service--lite .type-service__item {
    margin-top: 10px !important;
  }
  .type-service--lite .type-service__item:first-child {
    margin-top: 0 !important;
  }
}

@media (min-width: 1200px) {
  .type-service--lite .type-service__item a {
    padding: 47px 120px 43px 60px;
  }
}

.type-service--lite .type-service__item-icon {
  position: absolute;
  top: 32px;
  right: 32px;
  width: 32px;
  height: auto;
  background-image: none;
}

@media (min-width: 1200px) {
  .type-service--lite .type-service__item-icon {
    top: 46px;
    right: 51px;
    width: 40px;
  }
}

.type-service--lite .type-service__item-heading {
  font-weight: 400;
  letter-spacing: -0.025em;
}

@media (min-width: 1200px) {
  .type-service--lite .type-service__item-heading {
    margin-bottom: 5px;
  }
}

@media (min-width: 1200px) {
  .type-service--lite .type-service__item-text {
    margin-bottom: 20px;
    font-size: 16px;
  }
}

.type-service--lite .type-service__item-link {
  font-size: 12px;
}

.type-service__heading {
  margin-bottom: 20px;
  line-height: 1.08;
}

@media (min-width: 1200px) {
  .type-service__heading {
    margin-bottom: 42px;
  }
}

.type-service__text {
  margin-bottom: 40px;
  font-size: 14px;
  line-height: 1.2;
  color: #666666;
}

@media (min-width: 768px) {
  .type-service__text {
    margin-bottom: 60px;
  }
}

@media (min-width: 1200px) {
  .type-service__text {
    margin-bottom: 112px;
    font-size: 18px;
  }
}

.type-service__list {
  list-style: none;
  margin-top: 0;
  margin-bottom: 0;
  padding: 0;
}

.type-service__item {
  text-align: center;
}

.type-service__item a {
  display: block;
}

.type-service__item a:hover, .type-service__item a:focus {
  outline: none;
}

@media (min-width: 768px) {
  .type-service__item {
    text-align: left;
  }
}

.type-service__item + .type-service__item {
  margin-top: 32px;
}

@media (min-width: 768px) {
  .type-service__item + .type-service__item {
    margin-top: 0;
  }
}

.type-service__item-icon {
  display: flex;
  justify-content: center;
  height: 116px;
  margin: 0 auto 24px;
  background-size: 100% auto;
  transition: fill 0.3s ease;
}

@media (min-width: 768px) {
  .type-service__item-icon {
    margin-left: 0;
  }
}

@media (min-width: 1200px) {
  .type-service__item-icon {
    margin-bottom: 50px;
  }
}

.type-service__item-icon--interior {
  width: 120px;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' %3e%3cpath fill-rule='evenodd' fill='%23FAECE7' d='M87.606 9.356s25.108 9.985 31.488 33.144c6.38 23.16-22.618 64.286-47.401 69.451-24.784 5.166-68.704-8.565-71.402-46.028C-2.408 28.46 15.143 9.288 41.163 2.105c26.953-7.441 46.443 7.251 46.443 7.251z'/%3e%3c/svg%3e");
}

.type-service__item-icon--design {
  width: 115px;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' %3e%3cpath fill-rule='evenodd' fill='%23FAECE7' d='M58.534 113.411s-26.325 6.09-44.758-9.313c-18.433-15.404-18.017-65.724-.587-84.083 17.431-18.36 61.353-32.084 84.905-2.826 23.552 29.258 20.043 55.012 2.746 75.734-17.918 21.466-42.306 20.488-42.306 20.488z'/%3e%3c/svg%3e");
}

.type-service__item-icon--furniture {
  width: 120px;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' %3e%3cpath fill-rule='evenodd' fill='%23FAECE7' d='M12.544 83.901s-16.991-21.01-11.2-44.324C7.134 16.263 52.568-5.371 76.702 2.275c24.134 7.645 55.686 41.141 39.675 75.118-16.01 33.976-40.705 42.085-66.906 35.592-27.14-6.725-36.927-29.084-36.927-29.084z'/%3e%3c/svg%3e");
}

.type-service__item-icon svg {
  align-self: center;
  fill: #efb834;
}

.type-service__item-heading {
  margin: 0 0 16px;
  transition: color 0.3s ease;
  font-size: 24px;
  line-height: 1.11;
  color: #000000;
  font-weight: 300;
  /*a {
				color: $color-default-black;

				@include transition(color);

				&:hover,
				&:focus {
					outline: none;
					text-decoration: underline;
				}

				&:active {
					opacity: 0.7;
				}
			}*/
}

.type-service__item-heading:hover {
  color: #efb834;
}

@media (min-width: 768px) {
  .type-service__item-heading {
    max-width: 210px;
  }
}

@media (min-width: 1200px) {
  .type-service__item-heading {
    max-width: 270px;
    margin-bottom: 26px;
    font-size: 36px;
  }
}

.type-service__item-text {
  margin-bottom: 24px;
  font-size: 14px;
  line-height: 1.66;
  color: #666666;
  transition: color 0.3s ease;
}

@media (min-width: 768px) {
  .type-service__item-text {
    max-width: 275px;
  }
}

@media (min-width: 1200px) {
  .type-service__item-text {
    max-width: 320px;
    margin-bottom: 51px;
    font-size: 18px;
  }
}

.type-service__item-link {
  font-size: 14px;
  line-height: 1;
  color: #000000;
  text-transform: uppercase;
  font-weight: 600;
  transition: color 0.3s ease;
}

.type-service__item-link:hover, .type-service__item-link:focus {
  outline: none;
  color: #efb834;
}

.type-service__item-link:active {
  opacity: 0.7;
}

.type-service--parallax {
  background-color: #ffffff;
}

.type-service--parallax .type-service {
  /*&__heading {
				font-size: 26px;
				font-weight: 600;
				text-transform: capitalize;
				margin-bottom: 30px;
				text-align: center;

				@include media(md) {
					text-align: left;
				}

				@include media(xxl) {
					font-size: 54px;
					margin-bottom: 128px;
				}

				@media (max-height: 650px) {
					font-size: 20px;
					margin-bottom: 10px;
				}
			}

			&__list {
				@media (max-height: 650px) {
					flex-direction: row;
					align-items: flex-start;
				}
			}

			&__item {
				@media (max-height: 650px) {
					max-width: 33.3333%;
				}

				+ .type-service__item {
					margin-top: 45px;

					@include media(md) {
						margin-top: 0;
					}

					@media (max-height: 650px) {
						margin-top: 0;
					}
				}
			}

			&__item-icon {
				margin: 0 auto 5px;

				@include media(md) {
					margin: 0 0 24px;
				}

				@include media(xxl) {
					margin: 0 0 45px;
				}

				@media (max-width: 1199px) {
					width: 60px;
					height: 60px;
					background-image: none;
				}

				@media (max-width: 767px) {
					width: 40px;
					height: 40px;
				}

				@media (max-height: 650px) {
					margin-bottom: 5px;
					background-image: none;
				}

				svg {
					@media (max-width: 1199px) {
						width: 50%;
						height: 50%;
					}

					@media (max-width: 767px) {
						width: 80%;
						height: 80%;
					}
				}
			}

			&__item-heading {
				font-size: 18px;
				line-height: 20px;
				margin-bottom: 5px;
				font-weight: 600;

				@media (min-width: 768px) and (min-height: 651px) {
					font-size: 24px;
					line-height: 1.11;
					margin: 0 0 16px;
				}

				@include media(xxl) {
					font-size: 36px;
					line-height: 40px;
					margin: 0 0 28px;
				}
			}

			&__item-text {
				font-size: 12px;
				line-height: 14px;
				margin-bottom: 5px;

				@media (min-width: 768px) and (min-height: 651px) {
					font-size: 14px;
					line-height: 1.66;
					margin-bottom: 24px;
				}

				@include media(xxl) {
					font-size: 18px;
					line-height: 30px;
					margin-bottom: 46px;
				}
			}*/
}

.webpage--beige .type-service__item a {
  border-color: #aeab98;
  transition: background-color 0.3s ease, border-color 0.3s ease !important;
}

.webpage--beige .type-service__item a:hover, .webpage--beige .type-service__item a:focus {
  border-color: #4d524b;
  background-color: #4d524b;
}

.webpage--beige .type-service__item a:hover .type-service__item-heading,
.webpage--beige .type-service__item a:hover .type-service__item-text,
.webpage--beige .type-service__item a:hover .type-service__item-link, .webpage--beige .type-service__item a:focus .type-service__item-heading,
.webpage--beige .type-service__item a:focus .type-service__item-text,
.webpage--beige .type-service__item a:focus .type-service__item-link {
  color: #ffffff;
}

.webpage--beige .type-service__item a:hover .type-service__item-icon svg, .webpage--beige .type-service__item a:focus .type-service__item-icon svg {
  fill: #e6da89;
}

.webpage--beige .type-service__item-heading {
  max-width: 300px;
  font-family: "Cinzel", "Georgia", serif;
}

.webpage--beige .type-service__item-heading:hover {
  color: #e6da89 !important;
}

.webpage--beige .type-service__item-link {
  color: #000000;
}

.webpage--beige .type-service__item-icon svg {
  fill: #4d524b;
}

.webpage--beige .type-service__item .type-service__item-link:hover,
.webpage--beige .type-service__item .type-service__item-link:focus {
  color: #e6da89 !important;
}

.th-type-service__inner {
  padding: 40px;
}

@media (min-width: 1200px) {
  .th-type-service__inner {
    padding: 160px 15px 200px;
  }
}

.th-type-service__header {
  margin-bottom: 32px;
}

@media (min-width: 1200px) {
  .th-type-service__header {
    margin-bottom: 80px;
  }
}

.th-type-service__heading {
  display: inline-block;
  font-weight: 400;
}

@media (min-width: 1200px) {
  .th-type-service__heading {
    font-size: 60px;
  }
}

.th-type-service__heading + * {
  margin-top: 16px;
}

.th-type-service__text {
  max-width: 660px;
  font-size: 16px;
  line-height: 1.7;
  color: #666666;
}

@media (min-width: 1200px) {
  .th-type-service__text {
    font-size: 20px;
  }
}

.th-type-service__list {
  list-style: none;
  width: calc(100% + 80px);
  margin-left: -40px;
  padding: 0;
}

@media (min-width: 768px) {
  .th-type-service__list {
    width: 100%;
    margin: 0;
  }
}

.th-type-service__item {
  border-top: solid 1px #000000;
}

.th-type-service__item--quote {
  padding: 40px !important;
}

@media (min-width: 768px) {
  .th-type-service__item--quote {
    padding: 60px !important;
    border: none !important;
  }
}

@media (min-width: 1200px) {
  .th-type-service__item--quote {
    padding: 87px 54px 56px 100px !important;
  }
}

@media (min-width: 768px) {
  .th-type-service__item {
    border: solid 1px #000000;
  }
  .th-type-service__item:nth-child(even) {
    border-left: none;
  }
  .th-type-service__item:nth-child(n + 3) {
    border-top: none;
  }
}

.th-type-service__item a {
  position: relative;
  display: block;
  padding: 40px;
}

@media (min-width: 1200px) {
  .th-type-service__item a {
    padding: 64px 72px 56px;
  }
}

.th-type-service__item a:hover .th-type-service__item-heading, .th-type-service__item a:focus .th-type-service__item-heading {
  text-decoration: underline;
}

.th-type-service__item a:hover .th-type-service__item-link, .th-type-service__item a:focus .th-type-service__item-link {
  font-weight: 600;
}

.th-type-service__item a:hover .th-type-service__item-link::before, .th-type-service__item a:focus .th-type-service__item-link::before {
  opacity: 1;
}

.th-type-service__item a:active {
  opacity: 0.7;
}

.th-type-service__item-icon {
  margin-bottom: 48px;
}

@media (min-width: 1200px) {
  .th-type-service__item-icon {
    margin-bottom: 96px;
  }
}

.th-type-service__item-icon svg {
  display: block;
  width: 42px;
  height: auto;
}

@media (min-width: 1200px) {
  .th-type-service__item-icon svg {
    width: 54px;
  }
}

.th-type-service__item-heading {
  margin-bottom: 30px;
  font-size: 30px;
  line-height: 1.11;
  color: #000000;
}

@media (min-width: 1200px) {
  .th-type-service__item-heading {
    max-width: 276px;
    font-size: 36px;
  }
}

.th-type-service__item-text {
  font-size: 14px;
  line-height: 1.66;
  color: #666666;
}

@media (min-width: 1200px) {
  .th-type-service__item-text {
    font-size: 18px;
  }
}

.th-type-service__item-link {
  position: absolute;
  top: 40px;
  right: 40px;
  z-index: 1;
  font-size: 14px;
  line-height: 1;
  color: #000000;
  text-transform: uppercase;
}

@media (min-width: 1200px) {
  .th-type-service__item-link {
    top: 64px;
    right: 72px;
  }
}

.th-type-service__item-link::before {
  content: "";
  display: block;
  width: 100%;
  height: 14px;
  position: absolute;
  bottom: -40%;
  left: 0;
  z-index: -1;
  background-color: #ffdf91;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.th-type-service__quote {
  margin-bottom: 16px;
  font-size: 24px;
  line-height: 1.27;
  color: #000000;
  font-family: "EB Garamond", "Georgia", serif;
  font-style: normal;
  font-weight: 400;
}

@media (min-width: 1200px) {
  .th-type-service__quote {
    margin-bottom: 52px;
    font-size: 36px;
  }
}

.th-type-service__sign {
  margin-bottom: 12px;
}

@media (min-width: 1200px) {
  .th-type-service__sign {
    margin-bottom: 16px;
  }
}

.th-type-service__cite {
  font-size: 16px;
  line-height: 1;
  color: #000000;
  font-weight: 600;
  font-style: normal;
}

.th-type-service__cite span {
  color: #999999;
  font-weight: 400;
}

/* 4.40 Awards */
.an-awards {
  overflow: hidden;
  padding-top: 40px;
}

@media (min-width: 1200px) {
  .an-awards {
    padding-top: 83px;
  }
}

@media (min-width: 1200px) {
  .an-awards--about .an-awards__notice {
    padding-top: 71px;
  }
}

@media (min-width: 1200px) {
  .an-awards--about .an-awards__image {
    margin-top: -20px;
    padding-left: 120px;
  }
}

.an-awards--different {
  overflow: hidden;
}

@media (min-width: 1200px) {
  .an-awards--different {
    padding-top: 155px;
  }
}

@media (min-width: 1200px) {
  .an-awards--different .an-awards__intro {
    padding-top: 26px;
  }
}

@media (min-width: 1560px) {
  .an-awards--different .an-awards__intro {
    transform: translateX(171px);
  }
}

.an-awards--different .an-awards__text {
  max-width: none;
  font-size: 18px;
  line-height: 1.5;
  color: #999999;
}

@media (min-width: 1200px) {
  .an-awards--different .an-awards__text {
    font-size: 24px;
  }
}

@media (min-width: 1560px) {
  .an-awards--different .an-awards__text {
    margin-bottom: 107px;
  }
}

.an-awards--different .an-awards__heading {
  margin-bottom: 55px;
}

.an-awards__intro {
  margin-bottom: 20px;
}

@media (min-width: 768px) {
  .an-awards__intro {
    margin-bottom: 0;
  }
}

@media (min-width: 768px) {
  .an-awards__image--mb-negative {
    margin-bottom: -63px;
  }
}

@media (min-width: 992px) {
  .an-awards__image--mb-negative {
    margin-bottom: -97px;
  }
}

@media (min-width: 1560px) {
  .an-awards__image {
    width: 900px;
  }
}

.an-awards__image img {
  max-width: 100%;
  height: auto;
}

@media (min-width: 768px) {
  .an-awards__content {
    padding-right: 0;
  }
}

@media (min-width: 1560px) {
  .an-awards__content {
    padding-top: 26px;
    align-self: flex-start;
  }
}

.an-awards__heading {
  margin-bottom: 20px;
}

.an-awards__heading--upper {
  text-transform: uppercase;
}

@media (min-width: 992px) {
  .an-awards__heading {
    max-width: 340px;
  }
}

@media (min-width: 1200px) {
  .an-awards__heading {
    max-width: none;
  }
}

@media (min-width: 1560px) {
  .an-awards__heading {
    margin-bottom: 22px;
  }
}

.an-awards__text {
  max-width: 340px;
  margin-bottom: 32px;
  font-size: 16px;
  line-height: 1.88;
  color: #999999;
}

@media (min-width: 1200px) {
  .an-awards__text {
    font-size: 18px;
  }
}

@media (min-width: 1560px) {
  .an-awards__text {
    margin-bottom: 54px;
  }
}

.an-awards__buttons {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 40px;
}

@media (min-width: 1560px) {
  .an-awards__buttons {
    margin-bottom: 84px;
  }
}

.an-awards__btn {
  margin-right: 10px;
  align-self: flex-start;
}

@media (min-width: 1200px) {
  .an-awards__btn {
    margin-right: 27px;
  }
}

.an-awards__notice {
  max-width: 400px;
  margin-bottom: 24px;
  padding-right: 30px;
  padding-top: 24px;
  font-size: 14px;
  line-height: 1.625;
  color: #000000;
  border-top: solid 1px #d6d6d6;
}

@media (min-width: 1200px) {
  .an-awards__notice {
    font-size: 16px;
  }
}

@media (min-width: 1560px) {
  .an-awards__notice {
    margin-bottom: 46px;
    padding-top: 43px;
  }
}

.an-awards--parallax {
  background-repeat: no-repeat;
  background-size: cover;
  position: relative;
}

.an-awards--parallax .scroll-wrap::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.25);
}

.an-awards--parallax .row {
  margin: 0;
}

@media (min-width: 768px) {
  .an-awards--parallax .swiper-container {
    padding-bottom: 0;
  }
}

.an-awards--parallax .slide-counter {
  width: auto;
  position: absolute;
  top: 0;
  right: 0;
  left: unset;
  bottom: unset;
}

.an-awards--parallax .slide-counter .swiper-pagination-current {
  padding-right: 10px;
}

.an-awards--parallax .slide-counter .swiper-pagination-total {
  padding-left: 10px;
}

.an-awards--parallax .arrow-square {
  bottom: -60px;
  background-color: transparent;
  cursor: pointer;
}

@media (min-width: 992px) {
  .an-awards--parallax .arrow-square:hover {
    border-color: #f5480c;
  }
  .an-awards--parallax .arrow-square:hover svg {
    fill: #f5480c;
  }
}

@media (min-width: 1200px) {
  .an-awards--parallax .arrow-square {
    bottom: 0;
  }
}

.an-awards--parallax .arrow-square svg {
  fill: #ffffff;
}

.an-awards--parallax .arrow-square--prev {
  left: calc(50% - 50px);
}

@media (min-width: 1200px) {
  .an-awards--parallax .arrow-square--prev {
    left: unset;
    right: 72px;
  }
}

.an-awards--parallax .arrow-square--next {
  right: calc(50% - 50px);
}

@media (min-width: 1200px) {
  .an-awards--parallax .arrow-square--next {
    right: 0;
  }
}

.an-awards--parallax .an-awards__container {
  position: relative;
}

.an-awards--parallax .an-awards__outer {
  width: 100%;
}

.an-awards--parallax .an-awards__welcome {
  font-size: 18px;
  line-height: 30px;
  color: #ffffff;
  margin-bottom: 20px;
  max-width: 200px;
  /*@media (min-width: 1560px) and (min-height: 900px) {
					margin-bottom: 72px;
				}

				@media (max-height: 550px) {
					font-size: 20px;
				}*/
}

@media (min-width: 576px) {
  .an-awards--parallax .an-awards__welcome {
    max-width: 85%;
  }
}

@media (min-width: 768px) {
  .an-awards--parallax .an-awards__welcome {
    margin-bottom: 32px;
  }
}

@media (min-width: 1200px) {
  .an-awards--parallax .an-awards__welcome {
    margin-bottom: 72px;
  }
}

.an-awards--parallax .an-awards__heading {
  font-size: 36px;
  font-weight: 600;
  color: #ffffff;
  text-transform: capitalize;
  margin-bottom: 20px;
  /*@media (min-width: 1560px) and (min-height: 900px) {
					font-size: 64px;
					line-height: 72px;
				}

				@media (max-height: 550px) {
					font-size: 24px;
				}*/
}

.an-awards--parallax .an-awards__heading br {
  display: none;
}

@media (min-width: 992px) {
  .an-awards--parallax .an-awards__heading {
    font-size: 48px;
    max-width: 100%;
  }
  .an-awards--parallax .an-awards__heading br {
    display: block;
  }
}

@media (min-width: 1200px) {
  .an-awards--parallax .an-awards__heading {
    font-size: 64px;
    line-height: 72px;
  }
}

@media (min-width: 1560px) {
  .an-awards--parallax .an-awards__heading {
    margin-bottom: 32px;
  }
}

.an-awards--parallax .an-awards__content {
  padding: 0;
}

.an-awards--parallax .an-awards__text {
  max-width: 400px;
  color: #ffffff;
  margin-bottom: 25px;
  /*@media (min-width: 1560px) and (min-height: 500px) {
					margin-bottom: 80px;
				}

				@media (min-width: 1560px) and (min-height: 950px) {
					margin-bottom: 112px;
				}

				@media (max-height: 550px) {
					font-size: 16px;
					max-width: 100%;
				}*/
}

.an-awards--parallax .an-awards__text br {
  display: none;
}

@media (min-width: 992px) {
  .an-awards--parallax .an-awards__text br {
    display: none;
  }
}

@media (min-width: 1200px) {
  .an-awards--parallax .an-awards__text {
    margin-bottom: 112px;
  }
}

.an-awards--parallax .an-awards__buttons {
  margin-bottom: 0;
}

.an-awards--parallax .an-awards__buttons a {
  font-size: 16px;
  padding: 20px 22px;
  border-radius: 0;
  font-weight: 600;
}

.webpage--beige .an-awards__heading {
  font-family: "Cinzel", "Georgia", serif;
  color: #ffffff;
}

@media (min-width: 1200px) {
  .webpage--beige .an-awards__heading {
    font-size: 90px;
    line-height: 1;
    letter-spacing: normal;
  }
}

.webpage--beige .an-awards__text {
  color: #cccccc;
  line-height: 1.5;
}

/* 4.41 Works */
@media (min-width: 1560px) {
  .works__inner {
    max-width: 1550px;
  }
}

@media (min-width: 1560px) {
  .works__inner--width-large {
    max-width: 1830px;
  }
}

.works__header {
  max-width: 1520px;
  margin: 0 auto 30px;
}

@media (min-width: 768px) {
  .works__header {
    display: flex;
    flex-wrap: wrap;
  }
}

@media (min-width: 992px) {
  .works__header {
    margin-bottom: 50px;
  }
}

@media (min-width: 1200px) {
  .works__header {
    margin-bottom: 62px;
  }
}

.works__filter {
  margin-right: auto;
}

.works__sort {
  margin-top: 20px;
}

@media (min-width: 768px) {
  .works__sort {
    margin: 0 0 0 20px;
  }
}

.works__list {
  list-style: none;
  margin-top: 0;
  margin-bottom: 0;
  padding: 0;
}

.works__list--masonry {
  margin-left: -15px;
  margin-right: -15px;
}

.works__list--masonry .works__item {
  margin-bottom: 28px;
  padding: 0 15px;
}

@media (min-width: 576px) {
  .works__list--masonry .works__item {
    width: 50%;
  }
}

@media (min-width: 768px) {
  .works__list--masonry .works__item {
    width: 33.33%;
  }
}

@media (min-width: 1200px) {
  .works__list--masonry .works__item {
    width: 25%;
  }
}

.works__list--masonry .works__item--long {
  grid-row: auto / span 2;
  order: 2;
}

@media (min-width: 1200px) {
  .works__list--masonry .works__item--long {
    order: 0;
  }
}

.works__list--masonry .works__item--short {
  order: 1;
}

@media (min-width: 1200px) {
  .works__list--masonry .works__item--short {
    order: 0;
  }
}

.works__list--masonry .works__item.work-card--compact {
  margin-bottom: 30px;
}

.works__item {
  margin-bottom: 40px;
}

.works__item.work-card--compact {
  margin: 0;
}

@media (min-width: 1200px) {
  .works__item {
    margin-bottom: 75px;
  }
}

.works__more {
  margin: 120px auto 0;
}

.works--parallax {
  background-color: #ffffff;
  overflow-x: hidden;
}

.works--parallax .slide-counter-2 {
  min-width: 24px;
  height: 58px;
  display: inline-flex;
  width: auto;
  font-size: 18px;
  color: #ffffff;
  padding: 15px;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
  text-align: center;
  background-color: rgba(0, 0, 0, 0.15);
  border-bottom-right-radius: 5px;
}

@media (min-width: 1700px) {
  .works--parallax .slide-counter-2 {
    flex-direction: column;
    height: auto;
    top: -7px;
    left: -8px;
    background-color: transparent;
    color: #999999;
    padding: 0;
  }
}

.works--parallax .slide-counter-2 .swiper-pagination-current {
  color: #ffffff;
  position: relative;
  border-right: 1px solid #999999;
  padding-right: 10px;
  margin-right: 10px;
}

@media (min-width: 1700px) {
  .works--parallax .slide-counter-2 .swiper-pagination-current {
    border-right: none;
    border-bottom: 1px solid #999999;
    padding-right: 0;
    margin-right: 0;
    padding-bottom: 10px;
    margin-bottom: 10px;
    color: #efb834;
  }
}

.works--parallax .works-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 34px;
  height: 34px;
  padding: 0;
  border: 1px solid #ffffff;
  background-color: rgba(0, 0, 0, 0.25);
  position: absolute;
  bottom: 7px;
  z-index: 3;
  cursor: pointer;
  transition: border-color 0.3s ease;
}

@media (min-width: 992px) {
  .works--parallax .works-arrow:hover:not(.swiper-button-disabled) {
    border-color: #efb834;
  }
  .works--parallax .works-arrow:hover:not(.swiper-button-disabled) svg {
    fill: #efb834;
  }
}

@media (min-width: 992px) and (min-width: 1700px) {
  .works--parallax .works-arrow:hover:not(.swiper-button-disabled) {
    border-color: #ffffff;
  }
}

@media (min-width: 1700px) {
  .works--parallax .works-arrow {
    width: 24px;
    height: 24px;
    left: -7px;
    background-color: #ffffff;
  }
}

.works--parallax .works-arrow.swiper-button-disabled svg {
  fill: #cccccc;
}

@media (min-width: 1700px) {
  .works--parallax .works-arrow.swiper-button-disabled svg {
    fill: #999999;
  }
}

.works--parallax .works-arrow svg {
  width: 60%;
  height: 60%;
  fill: #ffffff;
  transition: fill 0.3s ease;
}

@media (min-width: 1700px) {
  .works--parallax .works-arrow svg {
    width: 100%;
    height: 100%;
    fill: #000000;
  }
}

.works--parallax .works-arrow--prev {
  left: calc(50% - 44px);
}

@media (min-width: 1700px) {
  .works--parallax .works-arrow--prev {
    left: -7px;
    bottom: 80px;
  }
}

.works--parallax .works-arrow--prev svg {
  transform: rotate(180deg);
}

.works--parallax .works-arrow--next {
  right: calc(50% - 44px);
}

@media (min-width: 1700px) {
  .works--parallax .works-arrow--next {
    bottom: 0;
  }
}

.works--parallax .works-arrow--next::before {
  content: "";
  display: none;
  width: 100%;
  height: 1px;
  background-color: #999999;
  position: absolute;
  top: -30px;
  left: 0;
}

@media (min-width: 1700px) {
  .works--parallax .works-arrow--next::before {
    display: block;
  }
}

.works--parallax .slide__content {
  position: absolute;
  top: 0;
  right: 0;
  max-width: 50%;
  min-height: 60px;
  padding: 16px 24px;
  background-color: #ffffff;
  box-sizing: border-box;
}

.works--parallax .slide__content:hover {
  background-color: #efb834;
}

@media (min-width: 576px) {
  .works--parallax .slide__content {
    top: unset;
    right: unset;
    left: -1px;
    bottom: -1px;
    max-width: 100%;
    height: auto;
    padding: 24px 32px;
  }
}

@media (min-width: 768px) {
  .works--parallax .slide__content {
    padding: 48px 48px 43px;
  }
}

@media (min-width: 1560px) {
  .works--parallax .slide__content {
    padding: 67px 63px 43px;
  }
}

.works--parallax .slide-content__heading {
  font-weight: 600;
  font-size: 20px;
}

@media (min-width: 768px) {
  .works--parallax .slide-content__heading {
    font-size: 30px;
  }
}

@media (min-width: 1560px) {
  .works--parallax .slide-content__heading {
    font-size: 40px;
  }
}

.works--parallax .slide-content__detail {
  font-size: 16px;
  color: #666666;
  letter-spacing: 0.4px;
}

.works--parallax .slider__item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.works--parallax .works__slider-wrapper {
  position: relative;
  width: 100%;
  max-width: 1434px;
  margin: 0 auto;
}

@media (min-width: 1560px) {
  .works--parallax .works__slider-wrapper {
    max-width: 1257px;
  }
}

@media (min-width: 1700px) {
  .works--parallax .works__slider-wrapper {
    max-width: 100%;
    margin: 0 58px;
  }
}

.works--parallax .works__slider {
  max-width: 1434px;
  height: 46vh;
}

@media (min-width: 768px) {
  .works--parallax .works__slider {
    height: 65.4375vh;
  }
}

.works--parallax .works__all {
  font-size: 18px;
  color: #000000;
  letter-spacing: 1.08px;
  text-transform: uppercase;
  text-decoration: underline;
  position: absolute;
  bottom: -40px;
  left: 50%;
  transform: translateX(-50%);
}

@media (min-width: 992px) {
  .works--parallax .works__all {
    bottom: -30px;
  }
  .works--parallax .works__all:hover {
    text-decoration: none;
  }
}

@media (min-width: 1200px) and (min-height: 500px) {
  .works--parallax .works__all {
    bottom: -40px;
  }
}

@media (min-width: 1200px) and (min-height: 960px) {
  .works--parallax .works__all {
    bottom: -76px;
  }
}

.works-page {
  padding: 56px 0;
  background-color: #fefae9;
}

@media (min-width: 1200px) {
  .works-page {
    padding: 56px 0 170px;
  }
}

@media (min-width: 1560px) {
  .works-page__inner {
    max-width: 1550px;
  }
}

@media (max-width: 576px) {
  .works-page__inner {
    padding-left: 20px;
    padding-right: 20px;
  }
}

.works-page__header {
  margin-bottom: 40px;
}

@media (min-width: 992px) {
  .works-page__header {
    margin-bottom: 80px;
  }
}

@media (min-width: 1200px) {
  .works-page__header {
    margin-bottom: 100px;
  }
}

.works-page__heading {
  margin: 0 0 20px;
  font-weight: 400;
}

@media (min-width: 992px) {
  .works-page__heading {
    max-width: 700px;
  }
}

@media (min-width: 1200px) {
  .works-page__heading {
    max-width: 860px;
    margin-bottom: 50px;
  }
}

.works-page__text {
  font-size: 16px;
  line-height: 1.66;
  color: #666666;
}

@media (min-width: 992px) {
  .works-page__text {
    max-width: 795px;
  }
}

@media (min-width: 1200px) {
  .works-page__text {
    font-size: 18px;
  }
}

.works-page__text p {
  margin: 0;
}

.works-page__text p + p {
  margin-top: 16px;
}

.works-page--grid {
  padding-bottom: 0;
  /*.filter__item {
			font-weight: 400;
			position: relative;

			&::before {
				content: "";
				display: block;
				width: 100%;
				height: 14px;
				position: absolute;
				bottom: -40%;
				left: 0;
				z-index: -1;
				background-color: $color-cream-brulee;
				opacity: 0;
				@include transition(opacity);
			}

			&:hover {
				color: $color-default-black;

				&::before {
					opacity: 1;
				}
			}

			&--active {
				font-weight: 700;
				color: $color-default-black;

				&::before {
					opacity: 1;
				}
			}
		}*/
}

@media (min-width: 1200px) {
  .works-page--grid {
    padding: 78px 0 0;
  }
}

.works-page--grid .row {
  margin: 0;
}

.works-page--grid .works__list {
  width: calc(100% + 8px);
  margin-left: -4px;
}

.works-page--grid .work-card {
  width: 100%;
  margin-bottom: -4px;
}

@media (min-width: 768px) {
  .works-page--grid .work-card {
    width: calc(50% + 2px);
  }
  .works-page--grid .work-card:nth-child(even) {
    margin-left: -4px;
  }
}

.works-page--grid .works__header {
  max-width: 1200px;
  margin-bottom: 73px;
  padding: 0 15px;
}

.works-page--grid .works-page__inner {
  max-width: 1200px;
}

.works-page--grid .works-page__inner--wide {
  max-width: 100%;
  margin: 0;
  padding: 0;
}

/* 4.42 Single project */
.single-project {
  padding-top: 40px;
}

@media (min-width: 1200px) {
  .single-project {
    padding-top: 130px;
  }
}

.single-project--minimalist {
  padding-top: 260px;
}

.single-project--minimalist .single-project__header {
  padding-bottom: 60px;
  border-bottom: solid 1px #eaeaea;
}

@media (min-width: 1200px) {
  .single-project--minimalist .single-project__header {
    margin-bottom: 106px;
    padding-bottom: 80px;
  }
}

.single-project--minimalist .single-project__intro-text {
  padding-bottom: 0;
  border: none;
  text-align: center;
}

.single-project--minimalist .single-project__heading {
  margin-bottom: 60px;
  text-align: center;
}

@media (min-width: 1200px) {
  .single-project--minimalist .single-project__heading {
    margin-bottom: 100px;
  }
}

.single-project--minimalist .single-project__social {
  margin: 80px auto;
  justify-content: center;
}

@media (min-width: 1200px) {
  .single-project--minimalist .single-project__social {
    margin: 0 auto 114px;
  }
}

.single-project--minimalist .single-project__photo {
  max-width: 100%;
  margin-bottom: 60px;
  text-align: center;
}

@media (min-width: 1200px) {
  .single-project--minimalist .single-project__photo {
    margin-bottom: 230px;
  }
}

.single-project--minimalist .single-project__photo img {
  display: inline-block;
  vertical-align: top;
  max-width: 100%;
}

.single-project--minimalist .single-project__photo--panorama {
  margin: 100px 0;
}

@media (min-width: 1200px) {
  .single-project--minimalist .single-project__photo--panorama {
    margin-bottom: 280px;
  }
}

@media (min-width: 768px) {
  .single-project--minimalist .single-project__photo--stuck {
    max-width: none;
  }
  .single-project--minimalist .single-project__photo--stuck img {
    width: calc(50vw - 15px);
    max-width: none;
  }
}

.single-project--minimalist .single-project__photo--width-inherit img {
  width: auto;
  max-width: none;
}

.single-project--minimalist .single-project__liked {
  margin-bottom: 156px;
}

.single-project--with-sidebar {
  padding-top: 0;
  border-top: solid 1px #d6d6d6;
}

@media (min-width: 1200px) {
  .single-project--with-sidebar {
    padding-top: 0;
  }
  .single-project--with-sidebar .container-fluid {
    max-width: 1920px;
    margin: 0 auto;
  }
}

.single-project--with-sidebar .single-project__heading {
  margin-bottom: 20px;
  letter-spacing: -0.025em;
}

@media (min-width: 576px) {
  .single-project--with-sidebar .single-project__heading {
    font-size: 36px;
  }
}

@media (min-width: 1200px) {
  .single-project--with-sidebar .single-project__heading {
    margin-bottom: 34px;
  }
}

.single-project--with-sidebar .single-project__meta {
  margin-bottom: 40px;
}

@media (min-width: 1200px) {
  .single-project--with-sidebar .single-project__meta {
    margin-bottom: 250px;
  }
}

.single-project--with-sidebar .single-project__paginate {
  margin-bottom: 40px;
}

@media (min-width: 1200px) {
  .single-project--with-sidebar .single-project__paginate {
    margin-bottom: 60px;
  }
}

.single-project--with-sidebar .single-project__short {
  margin-bottom: 40px;
  font-size: 16px;
  line-height: 1.625;
  color: #666666;
}

@media (min-width: 1200px) {
  .single-project--with-sidebar .single-project__short {
    margin-bottom: 37px;
  }
}

.single-project--with-sidebar .single-project__aside-content {
  padding: 0 15px;
}

@media (min-width: 768px) {
  .single-project--with-sidebar .single-project__aside-content {
    padding: 0 24px;
  }
}

@media (min-width: 1200px) {
  .single-project--with-sidebar .single-project__aside-content {
    padding: 0 32px;
  }
}

@media (min-width: 1560px) {
  .single-project--with-sidebar .single-project__aside-content {
    padding: 0 70px;
  }
}

.single-project__inner--width-full {
  max-width: 1550px !important;
}

.single-project img {
  display: inline-block;
  vertical-align: top;
  max-width: 100%;
}

.single-project__header {
  margin-bottom: 30px;
}

@media (min-width: 768px) {
  .single-project__header {
    margin-bottom: 60px;
  }
}

@media (min-width: 992px) {
  .single-project__header {
    margin-bottom: 80px;
  }
}

@media (min-width: 1200px) {
  .single-project__header {
    margin-bottom: 0;
  }
}

.single-project__heading {
  margin-bottom: 40px;
  font-weight: 400;
}

@media (min-width: 1200px) {
  .single-project__heading {
    margin-bottom: 65px;
    font-size: 60px;
  }
}

.single-project__social {
  margin-bottom: 40px;
}

.single-project__bg-parallax {
  position: relative;
  margin-bottom: 60px;
}

@media (min-width: 1200px) {
  .single-project__bg-parallax {
    margin-bottom: 130px;
  }
}

.single-project__bg-parallax-image {
  /*position: absolute;
			top: 0;
			left: 50%;
			width: 100%;
			transform: translateX(-50%);*/
}

.single-project__bg-parallax-image img {
  display: block;
  max-width: 100%;
  height: auto;
  margin: auto;
}

.single-project__intro {
  margin-bottom: 60px;
}

@media (min-width: 1200px) {
  .single-project__intro {
    margin-bottom: 108px;
  }
}

.single-project__intro-text {
  padding-bottom: 60px;
  font-size: 20px;
  line-height: 1.6;
  color: #000000;
  letter-spacing: -0.015em;
  border-bottom: solid 1px #eaeaea;
}

@media (min-width: 1200px) {
  .single-project__intro-text {
    padding-bottom: 100px;
    font-size: 30px;
  }
}

.single-project__section {
  margin-bottom: 60px;
}

@media (min-width: 1200px) {
  .single-project__section {
    margin-bottom: 97px;
  }
}

.single-project__section-heading {
  margin: 0 0 20px;
  font-size: 30px;
  line-height: 1;
  color: #000000;
  font-weight: 400;
  letter-spacing: -0.025em;
}

@media (min-width: 768px) {
  .single-project__section-heading {
    margin-bottom: 36px;
    font-size: 40px;
  }
}

@media (min-width: 1200px) {
  .single-project__section-heading {
    margin-bottom: 39px;
    font-size: 48px;
  }
}

.single-project__section-text {
  margin-bottom: 20px;
  font-size: 14px;
  line-height: 1.6;
  color: #666666;
}

@media (min-width: 768px) {
  .single-project__section-text {
    margin-bottom: 30px;
    font-size: 18px;
  }
}

.single-project__section ul {
  padding: 0 0 0 20px;
}

.single-project__section ul li {
  margin-bottom: 16px;
  font-size: 14px;
  line-height: 1.6;
  color: #000000;
}

@media (min-width: 768px) {
  .single-project__section ul li {
    font-size: 18px;
  }
}

@media (min-width: 1200px) {
  .single-project__section ul li {
    margin-bottom: 20px;
  }
}

.single-project__section ul li:last-child {
  margin-bottom: 0;
}

.single-project__review {
  margin-bottom: 60px;
}

@media (min-width: 1200px) {
  .single-project__review {
    margin-bottom: 110px;
  }
}

.single-project__gallery {
  margin-bottom: 60px;
}

@media (min-width: 768px) {
  .single-project__gallery {
    margin-bottom: 100px;
  }
}

@media (min-width: 1200px) {
  .single-project__gallery {
    margin-bottom: 156px;
  }
}

.single-project__gallery-inner {
  margin-top: -30px;
}

@media (min-width: 1200px) {
  .single-project__gallery-inner {
    max-width: 1550px !important;
    margin-top: -40px;
  }
}

.single-project__gallery-image {
  margin-top: 30px;
}

.single-project__gallery-image img {
  display: block;
}

.single-project__liked {
  margin: 0 auto 80px;
}

/* 4.43 About */
.about {
  padding: 40px 0;
}

@media (min-width: 1200px) {
  .about {
    padding: 104px 0 201px;
  }
}

@media (min-width: 1200px) {
  .about--pb {
    padding: 104px 0 25px;
  }
}

.about__header {
  margin-bottom: 60px;
}

@media (min-width: 1200px) {
  .about__header {
    margin-bottom: 143px;
  }
}

.about__heading {
  max-width: 205px;
  margin: 0 0 20px;
  font-size: 16px;
  line-height: 1.7;
  color: #000000;
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

@media (min-width: 1200px) {
  .about__heading {
    margin-bottom: 36px;
  }
}

.about__subheading {
  font-weight: 400;
}

.about__statistics {
  margin-bottom: 60px;
}

@media (min-width: 1200px) {
  .about__statistics {
    margin-bottom: 143px;
  }
}

.about__panorama {
  margin-bottom: 60px;
}

@media (min-width: 1200px) {
  .about__panorama {
    margin-bottom: 150px;
  }
}

.about__panorama img {
  display: block;
  width: 100%;
  height: auto;
}

.about__simplicity {
  margin-bottom: 60px;
}

@media (min-width: 1200px) {
  .about__simplicity {
    margin-bottom: 121px;
  }
}

.webpage__partners {
  margin-bottom: 0px;
}

@media (min-width: 1200px) {
  .webpage__partners {
    margin-bottom: 0px;
  }
}

.webpage__partners .partners__inner {
  border-bottom: none;
}

@media (max-width: 576px) {
  .webpage__partners .partners__inner {
    padding-left: 20px;
    padding-right: 20px;
  }
}

@media (min-width: 1200px) {
  .webpage__partners .partners__list {
    margin-bottom: 0;
  }
}

.about__approach {
  margin-bottom: 60px;
}

@media (min-width: 1200px) {
  .about__approach {
    margin-bottom: 127px;
  }
}

.about__journal {
  margin-bottom: 30px;
}

.about__contacts {
  padding-top: 60px;
  border-top: solid 1px #e2e2e2;
}

@media (min-width: 1200px) {
  .about__contacts {
    padding-top: 137px;
  }
}

.about__testimonials + .about__approach {
  margin-top: 60px;
}

@media (min-width: 1200px) {
  .about__testimonials + .about__approach {
    margin-top: 186px;
  }
}

@media (min-width: 1200px) {
  .about__feedback {
    padding-top: 24px;
  }
}

.about__twitter-block {
  margin-bottom: 60px;
}

@media (min-width: 1200px) {
  .about__twitter-block {
    margin-bottom: 120px;
  }
}

/* 4.44 News list */
.news-list {
  overflow: hidden;
  /*
		News-list-2
	*/
}

.news-list__container {
  padding-top: 200px;
  padding-bottom: 60px;
}

@media (min-width: 768px) {
  .news-list__container {
    padding-bottom: 90px;
  }
}

@media (min-width: 1560px) {
  .news-list__container {
    padding-bottom: 187px;
  }
}

@media (max-width: 576px) {
  .news-list__container {
    padding-left: 20px;
    padding-right: 20px;
  }
}

.news-list__heading {
  font-size: 40px;
  line-height: 44px;
  font-weight: 400;
  text-align: center;
  color: #57695a;
}

@media (min-width: 768px) {
  .news-list__heading {
    font-size: 60px;
    line-height: 72px;
  }
}

.news-list__filter {
  display: flex;
  flex-wrap: nowrap;
  justify-content: flex-start;
  margin: 45px 0 30px;
  overflow-x: auto;
  white-space: nowrap;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: thin;
}

@media (min-width: 576px) {
  .news-list__filter {
    margin: 116px 0 58px;
    justify-content: center;
    flex-wrap: wrap;
    overflow-x: visible;
  }
}

.news-list__filter button {
  width: auto;
  min-width: max-content;
  font-weight: 400;
  margin-bottom: 15px;
}

@media (min-width: 768px) {
  .news-list__filter button {
    width: auto;
  }
}

.news-list__filter button:not(:last-child) {
  margin-right: 15px;
}

@media (min-width: 768px) {
  .news-list__filter button:not(:last-child) {
    margin-right: 45px;
  }
}

.news-list__more {
  margin: 40px auto;
}

@media (min-width: 768px) {
  .news-list__more {
    margin: 71px auto 60px;
  }
}

.news-list__filter-with-search {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  margin: 52px 0 38px;
}

@media (min-width: 992px) {
  .news-list__filter-with-search {
    flex-direction: row;
  }
}

.news-list__filter-with-search .filter {
  margin: 0;
}

.news-list__filter-with-search input {
  max-width: 270px;
  margin-top: 20px;
}

@media (min-width: 992px) {
  .news-list__filter-with-search input {
    width: 100%;
    margin-top: 0;
    margin-bottom: 15px;
  }
}

.news-list__filter-with-search--center {
  justify-content: center;
}

.news-list .news-join {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin: 70px 0;
}

@media (min-width: 992px) {
  .news-list .news-join {
    flex-direction: row;
    align-items: flex-end;
    justify-content: space-between;
    margin: 102px 0 131px;
  }
}

.news-list .news-join__col {
  width: 100%;
}

.news-list .news-join__col:first-child {
  max-width: 395px;
  margin-bottom: 30px;
  text-align: center;
}

@media (min-width: 992px) {
  .news-list .news-join__col:first-child {
    margin-bottom: 0;
    text-align: left;
  }
}

.news-list .news-join__col:last-child {
  max-width: 600px;
  padding-bottom: 3px;
}

.news-list .news-join__title {
  font-size: 36px;
  letter-spacing: -1.2px;
  margin-bottom: 20px;
}

@media (min-width: 992px) {
  .news-list .news-join__title {
    font-size: 48px;
    margin-bottom: 30px;
  }
}

.news-list .news-join__subtitle {
  font-size: 16px;
  line-height: 26px;
  color: #666666;
}

.news-list .news-join input {
  border: none;
}

.news-list__news {
  list-style: none;
  display: flex;
  flex-wrap: wrap;
  padding: 0;
}

@media (min-width: 768px) {
  .news-list__news {
    margin: 0 -31px;
  }
}

.news-list .news-card {
  padding: 0;
}

@media (min-width: 768px) {
  .news-list .news-card {
    padding: 0 31px;
  }
}

.news-list__more-2 {
  margin: 0 auto;
}

@media (min-width: 992px) {
  .news-list__more-2 {
    margin-bottom: 91px;
  }
}

/* 4.45 News slider */
.news-slider {
  position: relative;
  margin: 0;
  padding-top: 60px;
}

@media (min-width: 992px) {
  .news-slider {
    padding-top: 0;
  }
}

@media (min-width: 1560px) {
  .news-slider {
    margin: 0 -175px;
  }
}

.news-slider .swiper-pagination {
  position: absolute;
  bottom: 20px;
  z-index: 1;
}

@media (min-width: 1560px) {
  .news-slider .swiper-pagination {
    justify-content: flex-end;
    bottom: 64px;
    padding-right: 163px;
  }
}

.news-slider .swiper-pagination .swiper-pagination-bullet {
  background-color: #ffffff;
  opacity: 1;
}

.news-slider .swiper-pagination .swiper-pagination-bullet-active {
  background-color: #efb834;
}

.news-slider .slider__nav {
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
}

@media (min-width: 992px) {
  .news-slider .slider__nav {
    top: 50%;
    left: 0;
    right: 0;
    transform: translateY(-50%);
    background-color: red;
    z-index: 1;
  }
}

.news-slider .slider__nav-btn {
  width: 18px;
  height: 32px;
  padding: 0;
  border: none;
  overflow: hidden;
  background-color: transparent;
  appearance: none;
  cursor: pointer;
}

.news-slider .slider__nav-btn:hover, .news-slider .slider__nav-btn:focus {
  outline: none;
}

.news-slider .slider__nav-btn:hover svg, .news-slider .slider__nav-btn:focus svg {
  fill: #efb834;
}

.news-slider .slider__nav-btn--next {
  margin-left: 12px;
}

@media (min-width: 992px) {
  .news-slider .slider__nav-btn--next {
    right: 3.125vw;
    margin: 0;
  }
}

@media (min-width: 992px) {
  .news-slider .slider__nav-btn--prev {
    left: 3.125vw;
  }
}

.news-slider .slider__nav-btn.swiper-button-disabled {
  pointer-events: none;
  opacity: 0.5;
}

@media (min-width: 768px) {
  .news-slider .slider__nav-btn.swiper-button-disabled {
    border: solid 1px #ffffff;
    background-color: transparent !important;
  }
  .news-slider .slider__nav-btn.swiper-button-disabled svg {
    fill: #ffffff !important;
  }
}

.news-slider .slider__nav-btn svg {
  position: relative;
  left: 50%;
  display: block;
  width: auto;
  height: 100%;
  transform: translateX(-50%);
  transition: fill 0.3s ease;
}

@media (min-width: 992px) {
  .news-slider .slider__nav-btn svg {
    position: static;
    width: 34px;
    height: auto;
    transform: translateX(0);
    fill: #ffffff;
  }
}

@media (min-width: 992px) {
  .news-slider .slider__nav-btn {
    position: absolute;
    top: 0;
    transform: translateY(-50%);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    border: solid 1px #ffffff;
    transition: background-color 0.3s ease;
  }
  .news-slider .slider__nav-btn:hover, .news-slider .slider__nav-btn:focus {
    background-color: #ffffff;
  }
  .news-slider .slider__nav-btn:hover svg, .news-slider .slider__nav-btn:focus svg {
    fill: #000000;
  }
}

.news-slider__item {
  position: relative;
  height: 450px;
}

@media (min-width: 768px) {
  .news-slider__item {
    height: 600px;
  }
}

@media (min-width: 992px) {
  .news-slider__item {
    height: 700px;
  }
}

.news-slider__item::after {
  content: '';
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.7);
}

@media (min-width: 992px) {
  .news-slider__item::after {
    background-image: linear-gradient(to top, rgba(0, 0, 0, 0.5) 0%, rgba(0, 0, 0, 0) 56%);
  }
}

.news-slider__item > img {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  object-fit: cover;
}

.news-slider__content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
  z-index: 1;
  max-width: 830px;
  height: 100%;
  color: #ffffff;
  padding: 30px;
  text-align: center;
  box-sizing: border-box;
}

@media (min-width: 992px) {
  .news-slider__content {
    justify-content: flex-end;
    text-align: left;
    padding: 0 30px 60px 172px;
  }
}

.news-slider__date {
  font-size: 13px;
  margin-bottom: 11px;
}

.news-slider__date a {
  font-size: 14px;
  text-transform: uppercase;
  color: #ffffff;
  transition: color 0.3s ease;
}

@media (min-width: 992px) {
  .news-slider__date a:hover {
    color: #efb834;
  }
}

.news-slider__title {
  font-size: 36px;
  line-height: 38px;
  letter-spacing: -1.2px;
  margin-bottom: 36px;
  color: #ffffff;
  transition: color 0.3s ease;
}

@media (min-width: 992px) {
  .news-slider__title {
    font-size: 48px;
    line-height: 48px;
  }
  .news-slider__title:hover {
    color: #efb834;
  }
}

.news-slider__text {
  font-size: 16px;
  line-height: 26px;
}

/* 4.46 News card */
.news-card {
  margin-bottom: 45px;
}

@media (min-width: 768px) {
  .news-card {
    margin-bottom: 91px;
  }
}

.news-card__pic {
  height: 325px;
}

.news-card__pic img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.news-card__content {
  display: flex;
  flex-direction: column;
  padding-top: 29px;
  padding-left: 15px;
  padding-right: 15px;
  text-align: center;
}

@media (min-width: 768px) {
  .news-card__content {
    padding-left: 0;
    padding-right: 0;
    flex-direction: row;
    text-align: left;
  }
}

@media (min-width: 768px) {
  .news-card__date {
    max-width: 75px;
    margin-right: 64px;
  }
}

.news-card__date p {
  margin: 0;
}

.news-card__date .num {
  font-family: "Cinzel", sans-serif;
  font-size: 60px;
  line-height: 85px;
}

.news-card__date .month,
.news-card__date .year {
  font-size: 14px;
  line-height: 20px;
  text-transform: uppercase;
  color: #666666;
}

.news-card__title {
  padding-top: 11px;
}

.news-card__title > a {
  font-size: 24px;
  line-height: 36px;
  color: #000000;
  transition: color 0.3s ease;
}

@media (min-width: 992px) {
  .news-card__title > a:hover, .news-card__title > a:focus {
    color: #efb834;
  }
  .news-card__title > a:active {
    opacity: 0.7;
  }
}

.news-card__title .author {
  font-size: 14px;
  margin: 18px 0 0;
  letter-spacing: 0.7px;
}

.news-card__title .author > a {
  text-transform: uppercase;
  color: #efb834;
}

.news-card__title .author > a:hover, .news-card__title .author > a:focus {
  outline: none;
  color: #efb834;
  text-decoration: underline;
}

.news-card__title .author span,
.news-card__title .author span > a {
  color: #999999;
  text-transform: none;
  letter-spacing: normal;
  transition: color 0.3s ease;
}

.news-card__title .author span:hover:not(.delimiter), .news-card__title .author span:focus:not(.delimiter),
.news-card__title .author span > a:hover:not(.delimiter),
.news-card__title .author span > a:focus:not(.delimiter) {
  color: #efb834;
}

.news-card__title .author span.delimiter,
.news-card__title .author span > a.delimiter {
  padding: 0 10px;
}

.webpage--beige .news-card__date .num {
  color: #ffffff;
}

.webpage--beige .news-card__date .year,
.webpage--beige .news-card__date .month {
  color: #999999;
}

.webpage--beige .news-card__title a {
  color: #ffffff;
}

.webpage--beige .news-card__title a:hover, .webpage--beige .news-card__title a:focus {
  outline: none;
  color: #e6da89;
}

.webpage--beige .news-card__title .author {
  color: #e6da89;
}

.webpage--beige .news-card__title .author a:hover, .webpage--beige .news-card__title .author a:focus {
  outline: none;
  color: #e6da89 !important;
}

/* 4.47 Post with sidebar */
.post-sb-page__container {
  max-width: 1170px !important;
  padding: 60px 10px;
}

@media (min-width: 768px) {
  .post-sb-page__container {
    padding: 90px 10px;
  }
}

@media (min-width: 1560px) {
  .post-sb-page__container {
    padding: 90px 0 176px;
  }
}

.post-sb-page__main-pic {
  margin: 0;
  height: 400px;
}

@media (min-width: 1560px) {
  .post-sb-page__main-pic {
    margin: 0 -175px;
    height: 700px;
  }
}

.post-sb-page__main-pic img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.post-sb-page__wrapper {
  display: flex;
  flex-direction: column;
  margin-top: 28px;
  overflow: hidden;
}

@media (min-width: 992px) {
  .post-sb-page__wrapper {
    flex-direction: row;
    justify-content: space-between;
    margin-top: 94px;
  }
}

.post-sb {
  width: 100%;
  order: 1;
}

@media (min-width: 992px) {
  .post-sb {
    margin-right: 100px;
    order: 0;
  }
}

.post-sb__date {
  margin-bottom: 22px;
}

.post-sb__date a {
  font-size: 14px;
  text-transform: uppercase;
  color: #f5480c;
}

.post-sb__date span {
  font-size: 13px;
  color: #666666;
}

.post-sb__date span.delimiter {
  font-size: 14px;
  padding: 0 8px;
}

.post-sb__title {
  font-size: 30px;
  line-height: 38px;
  letter-spacing: -1.2px;
  text-align: center;
  margin-bottom: 35px;
}

@media (min-width: 992px) {
  .post-sb__title {
    font-size: 48px;
    line-height: 60px;
    text-align: left;
    margin-bottom: 63px;
  }
}

.post-sb__title br {
  display: none;
}

@media (min-width: 992px) {
  .post-sb__title br {
    display: block;
  }
}

.post-sb__title-inner {
  font-size: 24px;
  line-height: 28px;
  letter-spacing: -0.9px;
  margin-bottom: 25px;
}

@media (min-width: 992px) {
  .post-sb__title-inner {
    font-size: 36px;
    line-height: 29.93px;
    margin-bottom: 37px;
  }
}

.post-sb__subtitle {
  font-size: 20px;
  line-height: 26px;
  margin-bottom: 25px;
}

@media (min-width: 992px) {
  .post-sb__subtitle {
    font-size: 24px;
    line-height: 36px;
    margin-bottom: 40px;
  }
}

.post-sb__subtitle a {
  color: #f5480c;
  font-weight: 600;
}

@media (min-width: 992px) {
  .post-sb__subtitle a:hover {
    text-decoration: underline;
  }
}

.post-sb__subtitle br {
  display: none;
}

@media (min-width: 992px) {
  .post-sb__subtitle br {
    display: block;
  }
}

.post-sb__text {
  font-size: 16px;
  line-height: 26px;
  color: #666666;
}

@media (min-width: 992px) {
  .post-sb__text {
    font-size: 18px;
    line-height: 30px;
  }
}

.post-sb__text p {
  margin: 0;
}

.post-sb__text p + p {
  margin-top: 15px;
}

@media (min-width: 992px) {
  .post-sb__text p + p {
    margin-top: 30px;
  }
}

.post-sb__text a {
  color: #00ccff;
}

@media (min-width: 992px) {
  .post-sb__text a:hover {
    text-decoration: underline;
  }
}

.post-sb__review {
  margin: 40px 0;
}

@media (min-width: 992px) {
  .post-sb__review {
    margin: 84px 0 92px;
  }
}

.post-sb__review .twitter-block__wrapper {
  padding: 30px;
}

@media (min-width: 576px) {
  .post-sb__review .twitter-block__wrapper {
    padding: 66px 71px 55px;
  }
}

.post-sb__content-pic {
  margin: 35px 0 28px;
}

@media (min-width: 992px) {
  .post-sb__content-pic {
    margin: 85px 0 58px;
  }
}

.post-sb__content-pic img {
  width: 100%;
  height: 300px;
  object-fit: cover;
}

@media (min-width: 768px) {
  .post-sb__content-pic img {
    height: 400px;
  }
}

@media (min-width: 992px) {
  .post-sb__content-pic img {
    height: 500px;
  }
}

.post-sb__pic-caption {
  font-size: 14px;
  font-style: italic;
  letter-spacing: 0.35px;
  color: #999999;
  margin-top: 15px;
  text-align: center;
}

.post-sb__pic-caption a {
  color: #000000;
}

.post-sb__list {
  font-size: 16px;
  line-height: 26px;
  color: #666666;
  margin: 15px 0;
  padding-left: 0;
  list-style: none;
}

@media (min-width: 992px) {
  .post-sb__list {
    font-size: 18px;
    line-height: 30px;
    padding-left: 25px;
    margin: 32px 0 35px;
  }
}

.post-sb__list li {
  position: relative;
  padding-left: 20px;
}

.post-sb__list li + li {
  margin-top: 6px;
}

.post-sb__list li::before {
  content: '';
  display: block;
  width: 7px;
  height: 7px;
  border-radius: 50%;
  background-color: #666666;
  position: absolute;
  top: 12px;
  left: 1px;
}

.post-sb__bottom {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 45px;
}

@media (min-width: 992px) {
  .post-sb__bottom {
    flex-direction: row;
    justify-content: space-between;
    margin-top: 93px;
  }
}

.post-sb__tags {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.post-sb__tag-item {
  padding: 1.5px 9px;
  margin-bottom: 5px;
  background-color: #ffffff;
  color: #999999;
  border: 1px solid #dadada;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.post-sb__tag-item:hover {
  background-color: #f5480c;
  border: 1px solid #f5480c;
  color: #ffffff;
}

.post-sb__tag-item:not(:last-child) {
  margin-right: 5px;
}

.post-sb__socials {
  display: flex;
  flex-direction: column;
  align-items: center;
}

@media (min-width: 992px) {
  .post-sb__socials {
    flex-direction: row;
  }
}

.post-sb__socials > p {
  font-size: 14px;
  line-height: 17px;
  letter-spacing: 0.7px;
  text-transform: uppercase;
  margin: 30px 0 10px;
}

@media (min-width: 992px) {
  .post-sb__socials > p {
    margin: 0 20px 0 0;
  }
}

.post-sb__social .social__item + .social__item {
  margin-left: 7px;
}

.post-sb__social .social__link {
  width: 30px;
  height: 30px;
  transition: all 0.3s ease;
}

@media (min-width: 992px) {
  .post-sb__social .social__link:hover {
    background-color: #ffffff;
  }
}

.post-sb__social .social__link svg {
  fill: #ffffff;
}

.post-sb__related-title {
  font-size: 36px;
  color: #16171e;
  margin-bottom: 39px;
}

.post-sb__related-wrapper {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10px;
}

@media (min-width: 1560px) {
  .post-sb__related-wrapper {
    margin: 0 -25px;
  }
}

.post-author {
  display: flex;
  flex-direction: column;
  align-items: center;
  border-top: 1px solid #e5e5e5;
  border-bottom: 1px solid #e5e5e5;
  padding: 25px 0;
  margin: 45px 0;
}

@media (min-width: 992px) {
  .post-author {
    flex-direction: row;
    align-items: flex-start;
    padding: 43px 0 50px;
    margin: 78px 0 71px;
  }
}

.post-author__photo {
  max-width: 80px;
  width: 100%;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  margin-bottom: 15px;
}

@media (min-width: 992px) {
  .post-author__photo {
    margin-right: 20px;
    margin-top: 7px;
    margin-bottom: 0;
  }
}

.post-author__photo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.post-author__name {
  font-size: 24px;
  text-align: center;
  margin-bottom: 2px;
}

@media (min-width: 992px) {
  .post-author__name {
    text-align: left;
  }
}

.post-author__position {
  font-size: 12px;
  text-transform: uppercase;
  color: #999999;
  text-align: center;
  margin-bottom: 11px;
}

@media (min-width: 992px) {
  .post-author__position {
    text-align: left;
  }
}

.post-author__text {
  font-size: 16px;
  line-height: 26px;
  color: #666666;
  text-align: center;
  margin-bottom: 26px;
}

@media (min-width: 992px) {
  .post-author__text {
    text-align: left;
  }
}

.post-author__socials {
  display: flex;
  align-items: center;
  justify-content: center;
  list-style: none;
  margin: 0;
  padding: 0;
}

@media (min-width: 992px) {
  .post-author__socials {
    justify-content: flex-start;
  }
}

.post-author__socials li + li {
  margin-left: 18px;
}

.post-author__socials li a {
  display: block;
  height: 18px;
}

@media (min-width: 992px) {
  .post-author__socials li a:hover svg {
    opacity: 0.5;
  }
}

.post-author__socials li a svg {
  transition: opacity 0.3s ease;
}

.post-related {
  padding: 0 10px;
  margin-bottom: 30px;
}

@media (min-width: 1560px) {
  .post-related {
    padding: 0 25px;
  }
}

.post-related__pic {
  height: 273px;
  margin-bottom: 31px;
}

.post-related__pic img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.post-related__date {
  font-size: 13px;
  text-transform: uppercase;
  color: #999999;
  margin-bottom: 12px;
}

.post-related__title {
  font-size: 24px;
  line-height: 32px;
  color: #000000;
  transition: color 0.3s ease;
}

@media (min-width: 992px) {
  .post-related__title:hover {
    color: #efb834;
  }
}

.comment {
  margin-top: 40px;
  margin-bottom: 40px;
  padding-top: 35px;
  border-top: 1px solid #e5e5e5;
}

@media (min-width: 768px) {
  .comment {
    margin-top: 71px;
    margin-bottom: 61px;
    padding-top: 67px;
  }
}

.comment__title {
  font-size: 36px;
  color: #16171e;
  margin-bottom: 37px;
  text-align: center;
}

@media (min-width: 768px) {
  .comment__title {
    text-align: left;
  }
}

.comment .comment-item {
  display: flex;
  flex-direction: column;
}

@media (min-width: 768px) {
  .comment .comment-item {
    flex-direction: row;
  }
}

.comment .comment-item + .comment-item {
  margin-top: 39px;
}

.comment .comment-item__photo {
  max-width: 70px;
  width: 100%;
  height: 70px;
  border-radius: 50%;
  overflow: hidden;
  margin-bottom: 10px;
}

@media (min-width: 768px) {
  .comment .comment-item__photo {
    margin-bottom: 0;
    margin-right: 20px;
  }
}

.comment .comment-item__photo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.comment .comment-item__top {
  display: flex;
  flex-direction: column;
  margin-bottom: 5px;
}

@media (min-width: 768px) {
  .comment .comment-item__top {
    flex-direction: row;
    justify-content: space-between;
  }
}

.comment .comment-item__name {
  font-size: 20px;
}

.comment .comment-item__sent {
  font-size: 13px;
  letter-spacing: 0.65px;
  text-transform: uppercase;
}

.comment .comment-item__text {
  font-size: 16px;
  line-height: 26px;
  color: #666666;
  margin-bottom: 17px;
}

.comment .comment-item__reply {
  width: 60px;
  padding: 5px 13px;
  position: relative;
  left: 50%;
  transform: translateX(-50%);
  background-color: #ffffff;
  border: 1px solid #cccccc;
  color: #181b31;
  font-size: 12px;
  text-align: center;
  text-transform: uppercase;
  box-sizing: border-box;
  cursor: pointer;
  transition: all 0.3s ease;
}

@media (min-width: 768px) {
  .comment .comment-item__reply {
    left: 0;
    transform: none;
  }
  .comment .comment-item__reply:hover {
    border-color: #f5480c;
    color: #f5480c;
  }
}

.comment .comment-item--replyed {
  padding-left: 10px;
  margin-left: 20px;
  border-left: 1px solid #e5e5e5;
}

@media (min-width: 768px) {
  .comment .comment-item--replyed {
    margin-left: 90px;
    padding-left: 0;
    border-left: none;
  }
}

.comment .comment-item--replyed .comment-item__photo {
  max-width: 45px;
  height: 45px;
}

.comment-form__title {
  font-size: 36px;
  color: #16171e;
  text-align: center;
  margin-bottom: 30px;
}

@media (min-width: 992px) {
  .comment-form__title {
    text-align: left;
    margin-bottom: 50px;
  }
}

.comment-form input,
.comment-form textarea {
  width: 100%;
  height: 46px;
  padding: 16px 20px;
  border: 1px solid #d4d4d4;
  background-color: #ffffff;
  box-sizing: border-box;
  transition: border-color 0.3s ease;
}

.comment-form input::placeholder,
.comment-form textarea::placeholder {
  font-size: 16px;
  opacity: 0.5;
  color: #000000;
}

.comment-form input:focus,
.comment-form textarea:focus {
  border-color: #000000;
}

.comment-form input:focus::placeholder,
.comment-form textarea:focus::placeholder {
  opacity: 0;
}

.comment-form textarea {
  height: 150px;
  resize: none;
}

.comment-form__inputs-group {
  display: flex;
  flex-direction: column;
  margin: 0 0 30px;
}

@media (min-width: 992px) {
  .comment-form__inputs-group {
    flex-direction: row;
    justify-content: space-between;
    margin: 0 -15px 30px;
  }
}

.comment-form__inputs-group .comment-form__field-wrapper {
  width: 100%;
  margin: 0 0 15px;
}

@media (min-width: 992px) {
  .comment-form__inputs-group .comment-form__field-wrapper {
    width: calc(33.3333% - 30px);
    margin: 0 15px;
  }
}

.comment-form__inputs-group .comment-form__field-wrapper label {
  margin-bottom: 0;
}

.comment-form__btn {
  width: 190px;
  height: 52px;
  padding: 10px;
  margin-top: 30px;
  border-radius: 0;
  position: relative;
  left: 50%;
  transform: translateX(-50%);
  cursor: pointer;
}

@media (min-width: 992px) {
  .comment-form__btn {
    left: 0;
    transform: none;
    margin-top: 44px;
  }
}

/* 4.48 Post without sidebar */
.post-page__container {
  max-width: 770px !important;
  padding: 60px 20px;
}

@media (min-width: 768px) {
  .post-page__container {
    padding: 90px 20px;
  }
}

@media (min-width: 1560px) {
  .post-page__container {
    padding: 90px 0 176px;
  }
}

.post-page__heading {
  margin-top: 50px;
  margin-bottom: 38px;
}

@media (min-width: 1560px) {
  .post-page__filter {
    margin: 0 -200px;
  }
}

.post-page__filter .news-list__filter-with-search input {
  width: 100%;
}


@media (min-width: 768px) {
  .post-page__main-pic {
    margin: 0 calc(-100vw / 2 + 370px);
  }
}

.post-page__main-pic img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.post-page__wrapper {
  display: flex;
  flex-direction: column;
  margin-top: 28px;
}

@media (min-width: 992px) {
  .post-page__wrapper {
    flex-direction: row;
    justify-content: space-between;
    margin-top: 94px;
  }
}

.post-sb--main {
  margin-right: 0;
}

.post-sb--main .post-sb__content-gallery {
  display: flex;
  flex-wrap: wrap;
  margin: 35px 0 0;
}

@media (min-width: 768px) {
  .post-sb--main .post-sb__content-gallery {
    margin-bottom: 28px;
  }
}

@media (min-width: 1200px) {
  .post-sb--main .post-sb__content-gallery {
    margin: 85px -215px 65px;
  }
}

.post-sb--main .post-sb__content-gallery-item {
  width: 100%;
  padding: 0 0 48px;
}

@media (min-width: 768px) {
  .post-sb--main .post-sb__content-gallery-item {
    padding: 0 10px 10px;
  }
}

@media (min-width: 1200px) {
  .post-sb--main .post-sb__content-gallery-item {
    padding: 0 15px 15px;
  }
}

.post-sb--main .post-sb__content-gallery-item img {
  width: 100%;
  object-fit: cover;
}

.post-sb--main .post-sb__pic-caption {
  margin-top: 10px;
}

.post-sb--main .post-sb__bottom {
  margin-top: 102px;
}

/* 4.49 Masonry */
@media (min-width: 768px) {
  .masonry {
    margin: 0 -5px;
  }
}

.masonry .masonry-item {
  width: 100%;
  margin-bottom: 10px;
}

@media (min-width: 768px) {
  .masonry .masonry-item {
    width: calc(50% - 10px);
    margin: 0 5px 10px;
  }
}

@media (min-width: 1560px) {
  .masonry .masonry-item {
    width: calc(25% - 10px);
  }
}

.masonry .masonry-item .article-preview__image {
  position: relative;
}

.masonry .masonry-item .article-preview__image::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background-image: linear-gradient(to top, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0) 53%);
}

.masonry .masonry-item .article-preview__image {
  height: auto;
}

/* 4.50 News masonry */
.news-masonry__container {
  max-width: 1830px !important;
  padding-bottom: 60px;
}

@media (min-width: 768px) {
  .news-masonry__container {
    padding-bottom: 130px;
  }
}

.news-masonry__more {
  margin: 40px auto;
}

@media (min-width: 768px) {
  .news-masonry__more {
    margin: 121px auto 60px;
  }
}

/* 4.51 News with sidebar */
.news-sb-page__container {
  max-width: 1170px !important;
  padding: 80px 10px 60px;
}

@media (min-width: 768px) {
  .news-sb-page__container {
    padding: 90px 10px;
  }
}

@media (min-width: 1560px) {
  .news-sb-page__container {
    padding: 107px 0 168px;
  }
}

.news-sb-page__title {
  font-size: 36px;
  letter-spacing: -1.2px;
  text-align: center;
}

@media (min-width: 768px) {
  .news-sb-page__title {
    font-size: 48px;
    text-align: left;
  }
}

.news-sb-page__related-slider {
  margin-top: 30px;
  position: relative;
}

@media (min-width: 1560px) {
  .news-sb-page__related-slider {
    margin-top: 57px;
  }
}

.news-sb-page .post-related {
  margin-bottom: 0;
  padding: 0;
}

@media (min-width: 768px) {
  .news-sb-page .post-related {
    margin-bottom: 30px;
  }
}

.news-sb-page__related-arrow {
  width: 43px;
  height: 43px;
  border: none;
  background-color: #ffffff;
  position: absolute;
  bottom: -40px;
  outline: none;
  cursor: pointer;
}

@media (min-width: 768px) {
  .news-sb-page__related-arrow {
    bottom: unset;
    top: -112px;
  }
}

@media (min-width: 992px) {
  .news-sb-page__related-arrow:hover svg {
    fill: #efb834;
  }
}

.news-sb-page__related-arrow.swiper-button-disabled svg {
  fill: #cccccc;
}

.news-sb-page__related-arrow svg {
  width: 100%;
  height: 100%;
  transition: fill 0.3s ease;
}

.news-sb-page__related-arrow--prev {
  left: calc(50% - 43px);
}

@media (min-width: 768px) {
  .news-sb-page__related-arrow--prev {
    left: unset;
    right: 66px;
  }
}

.news-sb-page__related-arrow--prev svg {
  transform: rotate(180deg);
}

.news-sb-page__related-arrow--next {
  right: calc(50% - 43px);
}

@media (min-width: 768px) {
  .news-sb-page__related-arrow--next {
    right: 12px;
  }
}

.news-sb-page__wrapper {
  display: flex;
  flex-direction: column;
  padding-top: 60px;
  margin-top: 100px;
  margin-bottom: 50px;
  border-top: 1px solid #dadada;
  overflow: hidden;
}

@media (min-width: 768px) {
  .news-sb-page__wrapper {
    margin-top: 60px;
    margin-bottom: 101px;
  }
}

@media (min-width: 992px) {
  .news-sb-page__wrapper {
    flex-direction: row;
    justify-content: space-between;
    padding-top: 104px;
    margin-top: 94px;
  }
}

.news-sb-slider {
  position: relative;
}

.news-sb-slider .arrow-square {
  bottom: -65px;
  cursor: pointer;
}

@media (min-width: 768px) {
  .news-sb-slider .arrow-square {
    bottom: unset;
    top: 39%;
  }
}

.news-sb-slider .arrow-square--prev {
  left: calc(50% - 60px);
}

@media (min-width: 768px) {
  .news-sb-slider .arrow-square--prev {
    left: 60px;
  }
}

.news-sb-slider .arrow-square--next {
  right: calc(50% - 60px);
}

@media (min-width: 768px) {
  .news-sb-slider .arrow-square--next {
    right: 60px;
  }
}

.news-sb-slider__item {
  position: relative;
  height: 500px;
}

@media (min-width: 768px) {
  .news-sb-slider__item {
    height: 600px;
  }
}

@media (min-width: 1200px) {
  .news-sb-slider__item {
    height: 800px;
  }
}

.news-sb-slider__pic {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.news-sb-slider__pic::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background-image: linear-gradient(to top, rgba(0, 0, 0, 0.5) 0%, rgba(0, 0, 0, 0) 56%);
}

.news-sb-slider__pic img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.news-sb-slider__content {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  height: 100%;
  padding: 25px 25px 45px;
  position: relative;
  color: #ffffff;
  text-align: center;
  box-sizing: border-box;
}

@media (min-width: 992px) {
  .news-sb-slider__content {
    padding: 56px 60px;
    text-align: left;
  }
}

.news-sb-slider__date {
  font-size: 13px;
  margin-bottom: 16px;
}

.news-sb-slider__date .tag,
.news-sb-slider__date .delimiter {
  font-size: 14px;
  text-transform: uppercase;
  color: #ffffff;
}

.news-sb-slider__date .tag {
  transition: color 0.3s ease;
}

@media (min-width: 992px) {
  .news-sb-slider__date .tag:hover {
    color: #efb834;
  }
}

.news-sb-slider__date .delimiter {
  padding: 0 10px;
}

.news-sb-slider__title {
  font-size: 30px;
  line-height: 36px;
  letter-spacing: -0.9px;
  color: #ffffff;
  transition: color 0.3s ease;
}

@media (min-width: 992px) {
  .news-sb-slider__title {
    font-size: 36px;
    line-height: 48px;
  }
  .news-sb-slider__title:hover {
    color: #efb834;
  }
}

.news-sb-slider__text {
  font-size: 16px;
  line-height: 26px;
  color: #cccccc;
  margin-top: 30px;
}

.news-sb {
  order: 1;
}

@media (min-width: 992px) {
  .news-sb {
    margin-right: 100px;
    order: 0;
  }
}

.news-sb__item {
  display: flex;
  flex-direction: column;
}

@media (min-width: 768px) {
  .news-sb__item {
    flex-direction: row;
  }
}

.news-sb__item + .news-sb__item {
  margin-top: 50px;
}

.news-sb__item--vertical {
  flex-direction: column;
}

.news-sb__item--vertical .news-sb__pic {
  max-width: 100%;
  height: 300px;
  margin-right: 0;
  margin-bottom: 37px;
}

@media (min-width: 768px) {
  .news-sb__item--vertical .news-sb__pic {
    height: 453px;
  }
}

.news-sb__pic {
  width: 100%;
  height: 300px;
  margin-bottom: 37px;
}

@media (min-width: 768px) {
  .news-sb__pic {
    max-width: 370px;
    height: 218px;
    margin-right: 50px;
    margin-bottom: 0;
  }
}

.news-sb__pic img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.news-sb__content {
  width: 100%;
  display: flex;
  flex-direction: column;
  border-bottom: 1px solid #dadada;
}

.news-sb__date {
  font-size: 13px;
  text-transform: uppercase;
  letter-spacing: 0.32px;
  color: #999999;
  margin-top: -4px;
  margin-bottom: 17px;
}

.news-sb__date .tag {
  font-size: 14px;
  letter-spacing: 0.7px;
  color: #efb834;
}

.news-sb__date .delimiter {
  font-size: 14px;
  padding: 0 10px;
}

.news-sb__title {
  font-size: 24px;
  line-height: 36px;
  margin-bottom: 22px;
  color: #000000;
  transition: color 0.3s ease;
}

@media (min-width: 992px) {
  .news-sb__title:hover {
    color: #efb834;
  }
}

.news-sb__text {
  font-size: 16px;
  line-height: 26px;
  color: #999999;
  margin-top: auto;
  margin-bottom: 38px;
}

/* 4.52 Error 404 */
.page-404__container {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: calc(100vh - 150px);
  padding: 60px 10px;
  text-align: center;
}

@media (min-width: 1200px) {
  .page-404__container {
    padding: 140px 0;
  }
}

.page-404__container img {
  width: 100%;
  max-width: 280px;
  margin-bottom: 32px;
}

@media (min-width: 768px) {
  .page-404__container img {
    max-width: 320px;
  }
}

@media (min-width: 992px) {
  .page-404__container img {
    max-width: 443px;
    margin-bottom: 54px;
  }
}

.page-404__title {
  font-size: 32px;
  margin-bottom: 14px;
}

@media (min-width: 768px) {
  .page-404__title {
    font-size: 42px;
  }
}

@media (min-width: 992px) {
  .page-404__title {
    font-size: 54px;
  }
}

.page-404__subtitle {
  font-size: 16px;
  color: #666666;
}

@media (min-width: 768px) {
  .page-404__subtitle {
    font-size: 18px;
  }
}

.page-404__btn {
  width: 220px;
  padding: 10px;
  border-radius: 0;
  margin-top: 32px;
  cursor: pointer;
}

@media (min-width: 768px) {
  .page-404__btn {
    font-size: 14px;
    padding: 21px 10px;
  }
}

@media (min-width: 992px) {
  .page-404__btn {
    margin-top: 54px;
  }
}

/* 4.53 Pricing */
.pricing--section .pricing__container {
  padding-top: 0;
  padding-bottom: 0;
}

.pricing--section .pricing__title,
.pricing--section .pricing__subtitle {
  margin-left: 0;
  text-align: left;
}

.pricing--section .pricing__title {
  font-weight: 400;
}

@media (min-width: 1200px) {
  .pricing--section .pricing__subtitle {
    margin-bottom: 94px;
  }
}

.pricing__container {
  max-width: 1170px !important;
  padding: 60px 10px;
}

@media (min-width: 1200px) {
  .pricing__container {
    padding: 127px 0 183px;
  }
}

.pricing__title, .pricing__subtitle {
  max-width: 730px;
  margin: 0 auto;
}

.pricing__title {
  font-size: 48px;
  line-height: 60px;
  letter-spacing: -1.5px;
  text-align: center;
  margin-bottom: 36px;
}

@media (min-width: 768px) {
  .pricing__title {
    font-size: 60px;
    line-height: 72px;
  }
}

.pricing__subtitle {
  font-size: 16px;
  line-height: 28px;
  letter-spacing: -0.27px;
  color: #666666;
  text-align: center;
  margin-bottom: 64px;
}

@media (min-width: 768px) {
  .pricing__subtitle {
    font-size: 18px;
    line-height: 32px;
    margin-bottom: 122px;
  }
}

.pricing__contact-us {
  max-width: 580px;
  margin: 45px auto 0;
  font-size: 20px;
  line-height: 32px;
  text-align: center;
}

@media (min-width: 768px) {
  .pricing__contact-us {
    font-size: 24px;
    line-height: 36px;
    margin: 81px auto 0;
  }
}

.pricing__contact-us a {
  color: #0099ff;
  text-decoration: underline;
}

@media (min-width: 992px) {
  .pricing__contact-us a:hover {
    text-decoration: none;
  }
}

.pricing__slider-container {
  position: relative;
  overflow: hidden;
  margin: 0 auto;
}

@media (min-width: 1200px) {
  .pricing__slider-container {
    margin: 0 -5px;
  }
}

.pricing__slider-container::before, .pricing__slider-container::after {
  content: "";
  position: absolute;
  top: 0;
  width: 35px;
  height: 100%;
  z-index: 2;
  pointer-events: none;
  transition: opacity 0.3s ease;
}

@media (min-width: 768px) {
  .pricing__slider-container::before, .pricing__slider-container::after {
    width: 100px;
  }
}

@media (min-width: 1200px) {
  .pricing__slider-container::before, .pricing__slider-container::after {
    display: none;
  }
}

.pricing__slider-container::before {
  left: 0;
  background: linear-gradient(90deg, white 0%, rgba(255, 255, 255, 0) 100%);
}

.pricing__slider-container::after {
  right: 0;
  background: linear-gradient(275deg, white 0%, rgba(255, 255, 255, 0) 100%);
}

.pricing__slider-container.hide-left::before {
  opacity: 0;
}

.pricing__slider-container.hide-right::after {
  opacity: 0;
}

.pricing__slider {
  width: 90%;
  overflow: visible;
}

@media (min-width: 768px) {
  .pricing__slider {
    width: 80%;
  }
}

@media (min-width: 1200px) {
  .pricing__slider {
    width: 100%;
  }
}

@media (min-width: 1200px) {
  .pricing__wrapper {
    display: flex;
    flex-wrap: wrap;
  }
}

.pricing-item {
  display: flex;
  padding: 0;
  height: auto;
}

@media (min-width: 1200px) {
  .pricing-item {
    width: calc(33.3333% - 10px);
    margin: 0 5px 10px;
  }
}

@media (min-width: 992px) {
  .pricing-item:hover .pricing-item__wrapper {
    border-color: #000000;
  }
}

.pricing-item__wrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  padding: 30px 20px;
  border: 1px solid #cccccc;
  background-color: #ffffff;
  box-sizing: border-box;
  transition: border-color 0.3s ease;
}

@media (min-width: 768px) {
  .pricing-item__wrapper {
    padding: 42px 50px 64px;
  }
}

.pricing-item__top {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
}

@media (min-width: 768px) {
  .pricing-item__top {
    flex-direction: row;
    justify-content: space-between;
  }
}

.pricing-item__plan {
  font-size: 16px;
  text-transform: uppercase;
  margin-bottom: 10px;
}

@media (min-width: 768px) {
  .pricing-item__plan {
    margin-bottom: 0;
  }
}

.pricing-item__caption span {
  padding: 3.5px 11px;
  background-color: #669900;
  font-size: 14px;
  color: #ffffff;
}

.pricing-item__price {
  font-family: "Cinzel", sans-serif;
  text-transform: uppercase;
  letter-spacing: -2px;
  margin-bottom: 7px;
  text-align: center;
}

@media (min-width: 768px) {
  .pricing-item__price {
    text-align: left;
  }
}

.pricing-item__price .currency {
  font-size: 30px;
  top: -0.7em;
}

.pricing-item__price .digit {
  font-size: 60px;
}

.pricing-item__price .square {
  font-size: 18px;
  letter-spacing: 0.3px;
}

.pricing-item__tax {
  font-size: 14px;
  color: #999999;
  text-align: center;
}

@media (min-width: 768px) {
  .pricing-item__tax {
    text-align: left;
  }
}

.pricing-item__included {
  list-style: none;
  border-top: 1px solid #d8d8d8;
  margin: 30px 0 50px;
  padding: 33px 0 0;
}

.pricing-item__included li {
  font-size: 16px;
  line-height: 22px;
  text-transform: uppercase;
  letter-spacing: 0.4px;
  padding: 15px 0;
  text-align: center;
}

@media (min-width: 768px) {
  .pricing-item__included li {
    text-align: left;
  }
}

.pricing-item__included li + li {
  border-top: 1px solid #d8d8d8;
}

.pricing-item__btn {
  font-size: 18px;
  text-align: center;
  text-transform: uppercase;
  color: #000000;
  border: 1px solid #999999;
  background-color: #ffffff;
  width: 100%;
  padding: 15px;
  margin-top: auto;
  border-radius: 0;
  cursor: pointer;
  transition: all 0.3s ease;
}

@media (min-width: 992px) {
  .pricing-item__btn:hover {
    border-color: #f5480c;
    background-color: #f5480c;
    color: #ffffff;
  }
}

.webpage--beige .pricing__slider-container::before, .webpage--beige .pricing__slider-container::after {
  display: none;
}

.webpage--beige .pricing__title {
  font-family: "Cinzel", "Georgia", serif;
  text-transform: lowercase;
}

.webpage--beige .pricing-item:hover .pricing-item__wrapper {
  border-color: #000000;
}

.webpage--beige .pricing-item__wrapper {
  background-color: transparent;
  border-color: #aeab98;
}

.webpage--beige .pricing-item__plan, .webpage--beige .pricing-item__price,
.webpage--beige .pricing-item__included li {
  color: #4d524b;
}

.webpage--beige .pricing-item__included {
  border-top-color: #aeab98;
}

.webpage--beige .pricing-item__included li + li {
  border-top-color: #aeab98;
}

.webpage--beige .pricing-item__caption span {
  color: #000000;
  background-color: #e6da89;
}

.webpage--beige .pricing-item__btn {
  color: #ffffff;
  background-color: #4d524b;
}

.webpage--beige .pricing-item__btn:hover, .webpage--beige .pricing-item__btn:focus {
  background-color: #e6da89;
  border-color: #e6da89;
}

/* 4.54 Services */
.services__container {
  max-width: 1170px !important;
  padding: 60px 10px;
}

@media (min-width: 1200px) {
  .services__container {
    padding: 118px 0 203px;
  }
}

.services .type-service {
  margin-bottom: 0;
  padding-bottom: 55px;
  border-bottom: 1px solid #dbdbdb;
}

@media (min-width: 992px) {
  .services .type-service {
    padding-bottom: 105px;
  }
}

.services .type-service__list {
  margin-right: 0;
  margin-left: 0;
}

@media (min-width: 1200px) {
  .services .type-service__list {
    margin-right: -15px;
    margin-left: -15px;
  }
}

.services .type-service__heading {
  font-size: 46px;
  line-height: 56px;
  font-weight: 400;
  letter-spacing: -1.35px;
  margin-bottom: 30px;
  text-align: center;
}

@media (min-width: 768px) {
  .services .type-service__heading {
    font-size: 54px;
    line-height: 64px;
    text-align: left;
    margin-bottom: 50px;
  }
}

.services .type-service__text {
  line-height: 32px;
  margin-bottom: 75px;
  text-align: center;
}

@media (min-width: 768px) {
  .services .type-service__text {
    text-align: left;
  }
}

@media (min-width: 992px) {
  .services .type-service__text {
    margin-bottom: 164px;
  }
}

.services .type-service__item-heading {
  max-width: 100%;
  font-weight: 400;
  text-align: center;
}

@media (min-width: 768px) {
  .services .type-service__item-heading {
    max-width: 280px;
    text-align: left;
  }
}

.services .statistics {
  margin-top: 55px;
  margin-right: 0;
  margin-left: 0;
}

@media (min-width: 1200px) {
  .services .statistics {
    margin-right: -15px;
    margin-left: -15px;
  }
}

@media (min-width: 992px) {
  .services .statistics {
    margin-top: 93px;
  }
}

.services .statistics__item-value {
  font-weight: 400;
}

.services .twitter-block {
  margin: 60px 0;
}

@media (min-width: 992px) {
  .services .twitter-block {
    margin: 149px 0 127px;
  }
}

/* 4.55 Contacts */
.contacts {
  padding: 40px 0;
}

@media (min-width: 768px) {
  .contacts {
    position: relative;
  }
}

@media (min-width: 992px) {
  .contacts {
    padding: 60px 0;
  }
}

@media (min-width: 1200px) {
  .contacts {
    padding: 90px 0;
  }
}

@media (min-width: 1560px) {
  .contacts__inner {
    max-width: 1550px;
  }
}

.contacts__header {
  margin-bottom: 60px;
}

@media (min-width: 1200px) {
  .contacts__header {
    margin-bottom: 138px;
    padding-top: 17px;
  }
}

.contacts__info {
  margin-top: 40px;
}

@media (min-width: 992px) {
  .contacts__info {
    margin-top: 0;
  }
}

@media (min-width: 1200px) {
  .contacts__info {
    margin-left: 30px;
  }
}

.contacts__heading {
  max-width: 310px;
  margin-bottom: 32px;
  font-weight: 400;
  letter-spacing: -0.025em;
}

@media (min-width: 1200px) {
  .contacts__heading {
    margin-bottom: 66px;
    font-size: 60px;
  }
}

.contacts__contact {
  max-width: 400px;
  background-color: #ffffff;
}

@media (min-width: 768px) {
  .contacts__contact {
    position: relative;
    padding: 40px;
  }
}

@media (min-width: 992px) {
  .contacts__contact {
    padding: 60px;
  }
}

@media (min-width: 1200px) {
  .contacts__contact {
    max-width: 595px;
    padding: 78px 83px 90px 100px;
  }
}

.contacts__modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  padding: 72px 40px;
  background-color: #ffffff;
  display: none;
}

@media (min-width: 768px) {
  .contacts__modal {
    position: absolute;
    z-index: 2;
    padding: 52px 60px;
  }
}

@media (min-width: 1200px) {
  .contacts__modal {
    padding: 72px 100px;
  }
}

.contacts__modal-close {
  position: absolute;
  top: 30px;
  right: 24px;
  width: 24px;
  height: 24px;
  padding: 0;
  border: none;
  background-color: transparent;
  cursor: pointer;
}

.contacts__modal-close svg {
  display: block;
  width: 100%;
  height: auto;
  transition: fill 0.3s ease;
}

.contacts__modal-close:hover, .contacts__modal-close:focus {
  outline: none;
}

.contacts__modal-close:hover svg, .contacts__modal-close:focus svg {
  fill: #efb834;
}

.contacts__modal-close:active {
  opacity: 0.7;
}

.contacts__modal-form {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
}

.contacts__modal-field {
  margin-bottom: 30px;
}

@media (min-width: 768px) {
  .contacts__modal-field {
    margin-bottom: 18px;
  }
}

@media (min-width: 1200px) {
  .contacts__modal-field {
    margin-bottom: 30px;
  }
}

.contacts__modal-btn {
  margin-top: 20px;
  align-self: flex-start;
}

@media (min-width: 1200px) {
  .contacts__modal-btn {
    margin-top: 34px;
  }
}

.contacts__map {
  height: 300px;
  margin-bottom: 40px;
  background-repeat: no-repeat;
  background-position: 50% 0;
  background-color: #999999;
}

@media (min-width: 1200px) {
  .contacts__map {
    margin-bottom: 137px;
  }
}

.contacts__map iframe {
  width: 100%;
  height: 100%;
  border: none;
}

@media (min-width: 1200px) {
  .contacts__map {
    height: 700px;
  }
}

@media (min-width: 768px) {
  .contacts__map--absolute {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
  }
}

/* 4.56 Services page */
.service-page {
  padding: 40px 0;
}

@media (min-width: 1200px) {
  .service-page {
    padding: 120px 0 201px;
  }
}

.service-page__header {
  margin-bottom: 60px;
}

@media (min-width: 1200px) {
  .service-page__header {
    margin-bottom: 120px;
  }
}

.service-page__heading {
  margin-bottom: 20px;
  font-weight: 400;
}

@media (min-width: 1200px) {
  .service-page__heading {
    margin-bottom: 55px;
  }
}

.service-page__image {
  max-width: 1550px;
  margin: 0 auto 60px;
  padding: 0 15px;
}

@media (min-width: 1200px) {
  .service-page__image {
    margin-bottom: 110px;
  }
}

.service-page__image img {
  display: block;
  width: 100%;
  height: auto;
}

.service-page__sidebar {
  margin-top: 60px;
}

@media (min-width: 992px) {
  .service-page__sidebar {
    margin-top: 0;
  }
}

.service-page__specialization {
  margin: 60px 0;
}

@media (min-width: 1200px) {
  .service-page__specialization {
    margin: 78px 0;
  }
}

.service-page__process {
  margin: 60px 0;
}

@media (min-width: 1200px) {
  .service-page__process {
    margin: 88px 0;
  }
}

@media (min-width: 1200px) {
  .service-page__text {
    max-width: 800px;
  }
}

.service-page__form-wrap {
  margin-top: 24px;
  padding-top: 80px;
  border-top: solid 1px #dfdfdf;
}

@media (min-width: 1200px) {
  .service-page__form-wrap {
    padding-top: 120px;
  }
}

.service-page h2 {
  font-size: 30px;
  line-height: 1.18;
  color: #000000;
  font-weight: 400;
  letter-spacing: -0.015em;
}

@media (min-width: 1200px) {
  .service-page h2 {
    margin-bottom: 34px;
    font-size: 36px;
  }
}

.service-page * + h2 {
  margin-top: 40px;
}

@media (min-width: 1200px) {
  .service-page * + h2 {
    margin-top: 78px;
  }
}

.service-page p {
  margin-top: 0;
  font-size: 16px;
  line-height: 1.77;
  color: #666666;
}

@media (min-width: 1200px) {
  .service-page p {
    font-size: 18px;
  }
}

.service-page .form__title {
  font-size: 36px;
  letter-spacing: -0.025em;
}

/* 4.57 Team */
.team__container {
  max-width: 1170px !important;
  padding: 60px 10px;
  overflow: hidden;
}

@media (min-width: 1200px) {
  .team__container {
    padding: 127px 0 191px;
  }
}

.team__title {
  font-size: 36px;
  line-height: 46px;
  text-align: center;
  letter-spacing: -1.5px;
  margin-bottom: 55px;
}

@media (min-width: 768px) {
  .team__title {
    font-size: 52px;
    line-height: 62px;
    margin-bottom: 74px;
  }
}

@media (min-width: 992px) {
  .team__title {
    font-size: 60px;
    line-height: 72px;
    margin-bottom: 172px;
  }
}

.team__wrapper {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}

@media (min-width: 480px) {
  .team__wrapper {
    margin: 0 -10px;
  }
}

@media (min-width: 992px) {
  .team__wrapper {
    margin: 0 -15px;
    justify-content: flex-start;
  }
}

@media (min-width: 1200px) {
  .team__wrapper {
    margin: 0 -30px;
  }
}

.team__item {
  margin-bottom: 46px;
}

@media (min-width: 480px) {
  .team__item {
    width: calc(50% - 20px);
    margin: 0 10px 46px;
  }
}

@media (min-width: 768px) {
  .team__item {
    width: calc(33.3333% - 20px);
  }
}

@media (min-width: 992px) {
  .team__item {
    width: calc(25% - 30px);
    margin: 0 15px 46px;
  }
}

@media (min-width: 1200px) {
  .team__item {
    width: calc(25% - 60px);
    margin: 0 30px 46px;
  }
}

.team__photo {
  height: 247px;
  overflow: hidden;
  border: 1px solid #999999;
  margin-bottom: 22px;
  padding-top: 20px;
  background-color: #ffffff;
}

@media (min-width: 992px) {
  .team__photo {
    filter: saturate(0);
    transition: filter 0.3s ease;
  }
}

.team__photo img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  object-position: top;
  pointer-events: none;
}

@media (min-width: 480px) {
  .team__photo img {
    object-fit: cover;
  }
}

.team__name {
  display: inline-block;
  font-size: 18px;
  line-height: 18px;
  color: #000000;
  text-transform: uppercase;
  letter-spacing: 0.45px;
  margin-bottom: 3px;
  position: relative;
  border-bottom: 1px solid transparent;
  transition: border-bottom-color 0.3s ease;
}

.team__position {
  font-size: 14px;
  color: #666666;
}

.team--join .team-item__photo {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  filter: saturate(1);
}

.team--join .team-item__photo svg {
  margin-bottom: 13px;
  transition: fill 0.3s ease;
}

.team--join p {
  font-size: 20px;
  line-height: 30px;
  font-weight: 400;
  text-transform: uppercase;
  text-align: center;
  color: #000000;
  margin: 0;
  transition: color 0.3s ease;
}

/* 4.58 Launch page */
.launch-page {
  width: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  background-size: cover;
  position: relative;
}

.launch-page::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
}

.launch-page__container {
  max-width: 750px;
  padding: 30px 10px;
  position: relative;
  margin: 0 auto;
}

@media (min-width: 768px) {
  .launch-page__container {
    padding: 60px 10px;
  }
}

@media (min-width: 992px) {
  .launch-page__container {
    padding: 60px 0;
  }
}

.launch-page__wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #ffffff;
  text-align: center;
}

.launch-page__logo .logo__image svg {
  fill: #ffffff;
}

.launch-page__logo .logo__text {
  text-align: left;
  color: #ffffff;
}

.launch-page__title {
  font-size: 42px;
  margin-top: 56px;
  margin-bottom: 19px;
}

@media (min-width: 992px) {
  .launch-page__title {
    font-size: 80px;
    letter-spacing: -2px;
  }
}

@media (min-height: 930px) {
  .launch-page__title {
    margin-top: 118px;
  }
}

.launch-page__subtitle {
  font-size: 16px;
  line-height: 28px;
  margin-bottom: 45px;
}

@media (min-width: 992px) {
  .launch-page__subtitle {
    font-size: 18px;
    line-height: 30px;
    letter-spacing: -0.27px;
  }
}

@media (min-height: 930px) {
  .launch-page__subtitle {
    margin-bottom: 87px;
  }
}

.launch-page__timer-title {
  font-size: 22px;
  line-height: 22px;
  text-transform: uppercase;
  margin-bottom: 30px;
}

@media (min-width: 768px) {
  .launch-page__timer-title {
    margin-bottom: 45px;
  }
}

@media (min-width: 992px) {
  .launch-page__timer-title {
    font-size: 24px;
    line-height: 24px;
  }
}

@media (min-height: 930px) {
  .launch-page__timer-title {
    margin-bottom: 72px;
  }
}

.launch-page__social {
  margin-top: 30px;
}

@media (min-width: 768px) {
  .launch-page__social {
    margin-top: 68px;
  }
}

@media (min-height: 930px) {
  .launch-page__social {
    margin-top: 119px;
  }
}

.launch-page__social .social__link svg {
  fill: #ffffff;
}

/* 4.59 Homepage parallax */
.homepage-parallax section {
  min-height: 100vh;
  overflow-y: auto;
  position: absolute;
  top: 0;
  left: 0;
  padding: 0;
}

.homepage-parallax section .scroll-wrap {
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
  min-height: 100%;
  padding: 110px 0;
}

.homepage-parallax .container {
  max-width: 1200px;
}

.homepage-parallax .contacts-parallax {
  background-image: url("../img/bg-our-story-2.jpg");
  background-repeat: no-repeat;
  background-size: cover;
}

.homepage-parallax .contacts-parallax__field {
  margin-bottom: 15px;
  border: none;
}

.homepage-parallax .contacts-parallax__field input,
.homepage-parallax .contacts-parallax__field textarea {
  background-color: #ffffff;
}

@media (max-height: 800px) {
  .homepage-parallax .contacts-parallax__field input,
  .homepage-parallax .contacts-parallax__field textarea {
    padding: 10px 18px;
  }
}

.homepage-parallax .contacts-parallax__btn {
  font-size: 16px;
  border-radius: 0;
  text-transform: uppercase;
  margin-top: 48px;
}

.homepage-parallax .contacts-parallax__btn:hover {
  color: #ffffff;
}

@media (max-height: 800px) {
  .homepage-parallax .contacts-parallax__btn {
    margin-top: 0;
  }
}

.homepage-parallax .contact-block__email a {
  font-size: 14px;
  color: #666666;
}

.homepage-parallax .contact-block__hint {
  font-size: 16px;
  font-weight: 600;
  color: #000000;
}

.homepage-parallax .contact-block__phone .contact-block__hint {
  font-size: 12px;
  color: #999999;
}

@media (max-height: 800px) {
  .homepage-parallax .contact-block__offices {
    margin-bottom: 0;
  }
}

.homepage-parallax .contact-block__offices .contact-block__hint {
  font-size: 14px;
}

/* 4.60 Team item */
.team-item {
  width: 45%;
  max-width: 247px;
}

@media (min-width: 992px) {
  .team-item:hover .team-item__photo {
    filter: saturate(1);
  }
  .team-item:hover .team-item__photo svg {
    fill: #efb834;
  }
  .team-item:hover .team-item__photo p {
    color: #efb834;
  }
}

.team-item__photo {
  overflow: hidden;
  border: 1px solid #999999;
  margin: 0 auto 22px;
}

@media (min-width: 992px) {
  .team-item__photo {
    filter: saturate(0);
    transition: filter 0.3s ease;
  }
}

.team-item__photo img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  object-position: top;
  pointer-events: none;
}

@media (min-width: 480px) {
  .team-item__photo img {
    object-fit: cover;
  }
}

.team-item__name {
  display: inline-block;
  font-size: 18px;
  line-height: 18px;
  color: #000000;
  text-transform: uppercase;
  letter-spacing: 0.45px;
  margin-bottom: 3px;
  position: relative;
  transition: color 0.3s ease;
}

@media (min-width: 992px) {
  .team-item__name:hover {
    color: #efb834;
  }
}

.team-item__position {
  font-size: 14px;
  color: #666666;
}

.team-item--join .team-item__photo {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  filter: saturate(1);
}

.team-item--join .team-item__photo svg {
  margin-bottom: 13px;
  transition: fill 0.3s ease;
}

.team-item--join p {
  font-size: 20px;
  line-height: 30px;
  font-weight: 400;
  text-transform: uppercase;
  text-align: center;
  color: #000000;
  margin: 0;
  transition: color 0.3s ease;
}

.webpage--beige .team-item:hover .team-item__photo {
  filter: saturate(1);
}

.webpage--beige .team-item:hover .team-item__photo svg {
  fill: #999999;
}

.webpage--beige .team-item:hover .team-item__photo p {
  color: #999999;
}

.webpage--beige .team-item__name:hover, .webpage--beige .team-item__name:focus {
  color: #e6da89;
}

/* 4.61 Our experts */
.our-experts--mt-negative {
  margin-top: -10px;
}

.our-experts__header {
  margin-bottom: 30px;
}

@media (min-width: 1200px) {
  .our-experts__header {
    margin-bottom: 71px;
  }
}

.our-experts__heading {
  color: #57695a;
  font-weight: 400;
  text-align: center;
}

@media (min-width: 1200px) {
  .our-experts__heading {
    font-size: 60px;
    line-height: 1;
  }
}

.our-experts__heading + * {
  margin-top: 42px;
}

.our-experts__text {
  font-size: 16px;
  line-height: 1.77;
  color: #666666;
  letter-spacing: -0.015em;
  max-width: 730px;
  text-align: center;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 64px;
  display: block;
}

@media (min-width: 1200px) {
  .our-experts__text {
    margin-bottom: 64px;
    font-size: 18px;
  }
}

.our-experts__list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-wrap: wrap;
  margin-top: -46px;
}

@media (min-width: 480px) {
  .our-experts__list {
    margin-left: -20px;
  }
}

@media (min-width: 992px) {
  .our-experts__list {
    margin-left: -30px;
  }
}

@media (min-width: 1200px) {
  .our-experts__list {
    margin-left: -60px;
  }
}

.our-experts__item {
  margin: 46px auto 0;
}

@media (min-width: 480px) {
  .our-experts__item {
    width: calc(50% - 20px);
    margin-left: 20px;
    margin-right: 0;
  }
}

@media (min-width: 768px) {
  .our-experts__item {
    width: calc(33.3333% - 20px);
  }
}

@media (min-width: 992px) {
  .our-experts__item {
    width: calc(25% - 30px);
    margin-left: 30px;
  }
}

@media (min-width: 1200px) {
  .our-experts__item {
    width: calc(25% - 60px);
    margin-left: 60px;
  }
}

.our-experts__line {
  padding-top: 40px;
}

@media (min-width: 1200px) {
  .our-experts__line {
    padding-top: 100px;
  }
}

@media (min-width: 1560px) {
  .our-experts__line {
    padding-top: 140px;
  }
}

.our-experts {
  margin-top: 112px;
}

@media (max-width: 576px) {
  .our-experts .our-experts__inner {
    padding-left: 20px;
    padding-right: 20px;
  }
}

.our-experts + .pricing {
  margin-top: -7px;
  margin-bottom: 48px;
}

.webpage--beige .our-experts__heading {
  font-family: "Cinzel", "Georgia", serif;
}

.webpage--beige .our-experts__line {
  border-bottom-color: #aeab98;
}

/* 4.62 Background wrapper */
.bg-wrapper {
  padding: 60px 0;
  background-color: #d8d5bf;
}

@media (min-width: 768px) {
  .bg-wrapper {
    padding: 100px 0;
  }
}

@media (min-width: 992px) {
  .bg-wrapper {
    padding: 160px 0;
  }
}

@media (min-width: 1200px) {
  .bg-wrapper {
    padding: 210px 0 200px;
    background-color: transparent;
    background-image: linear-gradient(to right, transparent 3.375vw, #d8d5bf 3.375vw);
  }
}

@media (min-width: 1560px) {
  .bg-wrapper {
    background-image: linear-gradient(to right, transparent 9.375vw, #d8d5bf 9.375vw);
  }
}

@media (min-width: 1200px) {
  .bg-wrapper--pb-less {
    padding-bottom: 158px;
  }
}

@media (min-width: 1200px) {
  .bg-wrapper--pb-less2 {
    padding-bottom: 170px;
  }
}

@media (min-width: 1200px) {
  .bg-wrapper--reverse {
    background-image: linear-gradient(to left, transparent 3.375vw, #d8d5bf 3.375vw);
  }
}

@media (min-width: 1560px) {
  .bg-wrapper--reverse {
    background-image: linear-gradient(to left, transparent 9.375vw, #d8d5bf 9.375vw);
  }
}

.bg-wrapper + .our-journal {
  margin-top: 60px;
}

@media (min-width: 1200px) {
  .bg-wrapper + .our-journal {
    margin-top: 200px;
  }
}

.bg-wrapper + .approach {
  margin-top: 30px;
}

.bg-wrapper + .rewards {
  margin-top: 15px;
}

/* 4.63 Works section */
.works-section {
  padding: 60px 0;
}

@media (min-width: 768px) {
  .works-section {
    padding: 80px 0;
  }
}

@media (min-width: 992px) {
  .works-section {
    padding: 120px 0;
  }
}

@media (min-width: 1200px) {
  .works-section {
    padding: 177px 0;
  }
}

.works-section__heading {
  margin: 0 0 32px;
  font-weight: 400;
}

@media (min-width: 768px) {
  .works-section__heading {
    margin-bottom: 48px;
  }
}

@media (min-width: 1200px) {
  .works-section__heading {
    margin-bottom: 113px;
  }
}

@media (min-width: 768px) {
  .works-section__column--list {
    margin-top: 32px;
  }
  .works-section__column--list .works-section__card {
    margin-left: auto;
  }
}

.works-section__card {
  margin-bottom: 60px;
}

@media (min-width: 768px) {
  .works-section__card {
    max-width: 400px;
    margin-bottom: 0;
  }
  .works-section__card--big {
    max-width: 600px;
  }
  .works-section__card + .works-section__card {
    margin-top: 56px;
  }
}

@media (min-width: 1200px) {
  .works-section__card + .works-section__card {
    margin-top: 96px;
  }
}

.works-section__card-image {
  margin-bottom: 24px;
}

@media (min-width: 1200px) {
  .works-section__card-image {
    margin-bottom: 42px;
  }
}

.works-section__card-image img {
  display: block;
  width: 100%;
  height: auto;
}

.works-section__card-tags {
  margin-bottom: 12px;
  font-size: 16px;
  line-height: 1;
  color: #efb834;
}

.works-section__card-heading {
  margin: 0 0 12px;
  font-size: 24px;
  line-height: 1;
  color: #000000;
  font-weight: 400;
  letter-spacing: 0.025em;
  text-transform: uppercase;
}

@media (min-width: 768px) {
  .works-section__card-heading {
    margin-bottom: 20px;
  }
}

@media (min-width: 1200px) {
  .works-section__card-heading {
    font-size: 36px;
  }
}

.works-section__card-heading a {
  color: #000000;
}

.works-section__card-heading a:hover, .works-section__card-heading a:focus {
  outline: none;
  text-decoration: underline;
}

.works-section__card-text {
  max-width: 550px;
  font-size: 14px;
  line-height: 1.625;
  color: #999999;
}

@media (min-width: 1200px) {
  .works-section__card-text {
    font-size: 16px;
  }
}

.works-section__btn {
  display: none;
}

@media (min-width: 768px) {
  .works-section__btn {
    display: inline-block;
    margin-top: 140px;
  }
}

.webpage--beige .works-section__heading {
  font-family: "Cinzel", "Georgia", serif;
  color: #ffffff;
  text-transform: uppercase;
  letter-spacing: normal;
}

.webpage--beige .works-section__card-tags {
  color: #e6da89;
}

.webpage--beige .works-section__card-heading a {
  color: #ffffff;
}

/* 4.64 Studio section */
@media (min-width: 768px) {
  .studio-section {
    overflow: hidden;
  }
}

.studio-section__header {
  margin-bottom: 40px;
}

@media (min-width: 1200px) {
  .studio-section__header {
    margin-bottom: 100px;
  }
}

.studio-section__heading {
  font-weight: 400;
}

.studio-section__heading + * {
  margin-top: 24px;
}

@media (min-width: 1200px) {
  .studio-section__heading + * {
    margin-top: 43px;
  }
}

.studio-section__text {
  max-width: 940px;
  font-size: 16px;
  line-height: 1.77;
  color: #999999;
}

@media (min-width: 1200px) {
  .studio-section__text {
    font-size: 18px;
  }
}

@media (min-width: 768px) {
  .studio-section__wrapper {
    display: flex;
    justify-content: flex-end;
  }
}

.studio-section__image {
  position: relative;
}

@media (min-width: 768px) {
  .studio-section__image {
    display: flex;
    width: 24.479vw;
    max-width: 470px;
    flex-shrink: 0;
  }
}

@media (min-width: 768px) {
  .studio-section__image--big {
    width: 54.427vw;
    max-width: 1045px;
  }
}

.studio-section__image + .studio-section__image {
  margin-top: 30px;
}

@media (min-width: 768px) {
  .studio-section__image + .studio-section__image {
    margin-top: 0;
    margin-left: 30px;
  }
}

.studio-section__image img {
  display: block;
  width: 100%;
  height: auto;
}

.studio-section__play-video {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translateX(-50%) translateY(-50%);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 115px;
  height: 115px;
  padding: 0 24px;
  font-size: 16px;
  line-height: 1.25;
  color: #ffffff;
  text-align: center;
  text-transform: uppercase;
  border: solid 1px rgba(255, 255, 255, 0.75);
  border-radius: 50%;
  background-color: rgba(77, 82, 75, 0.75);
  transition: background-color 0.3s ease;
  cursor: pointer;
}

.studio-section__play-video:hover, .studio-section__play-video:focus {
  outline: none;
  background-color: #4d524b;
  color: #ffffff;
}

.studio-section + .rewards {
  margin-top: -40px;
}

@media (min-width: 1200px) {
  .studio-section + .rewards {
    margin-top: -100px;
  }
}

.webpage--beige .studio-section__heading {
  font-family: "Cinzel", "Georgia", serif;
  color: #ffffff;
  text-transform: uppercase;
}

/* 4.65 Studio hero */
.studio-hero {
  overflow: hidden;
  padding: 60px 0;
}

@media (min-width: 768px) {
  .studio-hero {
    padding: 105px 0 117px;
  }
}

.studio-hero__intro {
  margin-bottom: 20px;
}

@media (min-width: 768px) {
  .studio-hero__intro {
    margin-bottom: 0;
  }
}

@media (min-width: 768px) {
  .studio-hero__content {
    padding-right: 0;
  }
}

@media (min-width: 1560px) {
  .studio-hero__content {
    padding-top: 77px;
    align-self: flex-start;
  }
}

.studio-hero__image img {
  max-width: 100%;
  height: auto;
}

@media (min-width: 1560px) {
  .studio-hero__image {
    padding-left: 72px;
  }
  .studio-hero__image img {
    max-width: none;
  }
}

.studio-hero__heading {
  margin-bottom: 20px;
  font-weight: 400;
  letter-spacing: normal;
}

@media (min-width: 992px) {
  .studio-hero__heading {
    max-width: 340px;
  }
}

@media (min-width: 1200px) {
  .studio-hero__heading {
    font-size: 72px;
    line-height: 1;
    max-width: none;
  }
}

@media (min-width: 1560px) {
  .studio-hero__heading {
    margin-bottom: 53px;
    font-size: 90px;
  }
}

.studio-hero__text {
  max-width: 3900px;
  margin-bottom: 32px;
  font-size: 18px;
  line-height: 1.5;
  color: #999999;
}

@media (min-width: 1200px) {
  .studio-hero__text {
    font-size: 24px;
  }
}

@media (min-width: 1560px) {
  .studio-hero__text {
    margin-bottom: 104px;
  }
}

.webpage--beige .studio-hero__heading {
  font-family: "Cinzel", "Georgia", serif;
  color: #ffffff;
}

/* 4.66 Technical drawing hero slider */
.th-hero-slider {
  position: relative;
}

@media (min-width: 768px) {
  .th-hero-slider {
    padding-top: 60px;
  }
}

@media (min-width: 992px) {
  .th-hero-slider {
    padding-top: 100px;
  }
}

@media (min-width: 1200px) {
  .th-hero-slider {
    padding-top: 140px;
  }
}

@media (min-width: 1200px) {
  .th-hero-slider__inner {
    position: relative;
    max-width: 1640px;
    margin: 0 auto;
  }
}

.th-hero-slider__counter {
  position: absolute;
  z-index: 2;
  top: 20px;
  left: 20px;
  display: flex;
  align-items: flex-start;
  font-size: 24px;
  line-height: 1;
  color: #000000;
}

@media (min-width: 768px) {
  .th-hero-slider__counter {
    top: 0;
  }
}

.th-hero-slider__counter span {
  display: inline-block;
  height: 24px;
  margin-right: 6px;
}

@media (min-width: 576px) {
  .th-hero-slider__counter span {
    color: #999999;
  }
}

.th-hero-slider__counter span:first-child {
  margin-top: -4px;
  font-size: 48px;
}

@media (min-width: 576px) {
  .th-hero-slider__counter span:first-child {
    color: #000000;
  }
}

.th-hero-slider__slide {
  position: relative;
  box-sizing: border-box;
}

@media (min-width: 576px) {
  .th-hero-slider__slide {
    display: flex;
  }
}

@media (min-width: 768px) {
  .th-hero-slider__slide {
    padding-left: 100px;
  }
}

@media (min-width: 992px) {
  .th-hero-slider__slide {
    padding-left: 150px;
  }
}

@media (min-width: 1560px) {
  .th-hero-slider__slide {
    padding-left: 230px;
  }
}

.th-hero-slider__content {
  z-index: 1;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 100px 20px 32px;
}

@media (min-width: 576px) {
  .th-hero-slider__content {
    padding-bottom: 100px;
    flex-grow: 1;
  }
}

@media (min-width: 768px) {
  .th-hero-slider__content {
    padding: 0 20px 80px;
  }
}

.th-hero-slider__image {
  position: relative;
  height: 410px;
}

@media (min-width: 576px) {
  .th-hero-slider__image {
    max-width: 40%;
    height: auto;
  }
}

@media (min-width: 992px) {
  .th-hero-slider__image {
    width: 50%;
    max-width: 510px;
  }
}

@media (min-width: 1560px) {
  .th-hero-slider__image {
    margin-right: 20px;
  }
}

.th-hero-slider__image img {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.th-hero-slider__heading {
  margin-bottom: 16px;
  font-size: 32px;
  line-height: 1.11;
  color: #000000;
}

@media (min-width: 992px) {
  .th-hero-slider__heading {
    font-size: 60px;
  }
}

@media (min-width: 1200px) {
  .th-hero-slider__heading {
    margin-bottom: 48px;
    font-size: 72px;
  }
}

@media (min-width: 1560px) {
  .th-hero-slider__heading {
    font-size: 90px;
  }
}

.th-hero-slider__text {
  max-width: 316px;
  margin-bottom: 20px;
  font-size: 16px;
  line-height: 1.6;
  color: #000000;
}

@media (min-width: 576px) {
  .th-hero-slider__text {
    color: #666666;
  }
}

@media (min-width: 992px) {
  .th-hero-slider__text {
    margin-bottom: 40px;
    font-size: 20px;
  }
}

@media (min-width: 1200px) {
  .th-hero-slider__text {
    margin-bottom: 80px;
  }
}

.th-hero-slider__circle {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 144px;
  height: 144px;
}

@media (min-width: 576px) {
  .th-hero-slider__circle {
    bottom: auto;
    right: auto;
    left: 0;
    top: 20%;
    transform: translateX(-33%);
  }
}

@media (min-width: 1200px) {
  .th-hero-slider__circle {
    top: 135px;
    width: 280px;
    height: 280px;
  }
}

@media (min-width: 1200px) {
  .th-hero-slider__circle {
    transform: translateX(-61%);
  }
}

.th-hero-slider__circle img {
  display: block;
  width: 100%;
  height: auto;
}

.th-hero-slider__nav {
  position: absolute;
  top: 20px;
  right: 10px;
  z-index: 1;
}

@media (min-width: 576px) {
  .th-hero-slider__nav {
    top: auto;
    right: auto;
    left: 10px;
    bottom: 10px;
  }
}

@media (min-width: 768px) {
  .th-hero-slider__nav {
    bottom: 15px;
  }
}

@media (min-width: 992px) {
  .th-hero-slider__nav {
    bottom: 34px;
  }
}

@media (min-width: 992px) {
  .th-hero-slider__nav {
    bottom: 42px;
  }
}

.th-hero-slider__nav-btn {
  padding: 0;
  background-color: transparent;
  border: none;
  cursor: pointer;
}

@media (min-width: 1200px) {
  .th-hero-slider__nav-btn + .th-hero-slider__nav-btn {
    margin-left: 36px;
  }
}

.th-hero-slider__nav-btn:hover, .th-hero-slider__nav-btn:focus {
  outline: none;
}

@media (min-width: 768px) {
  .th-hero-slider__pagination {
    position: absolute;
    left: 120px;
    width: 360px;
    margin: 0;
    transform: translateY(-100%);
  }
}

@media (min-width: 992px) {
  .th-hero-slider__pagination {
    left: 170px;
    transform: translateY(-120%);
  }
}

@media (min-width: 1200px) {
  .th-hero-slider__pagination {
    width: 480px;
  }
}

@media (min-width: 1560px) {
  .th-hero-slider__pagination {
    width: 580px;
    left: 250px;
  }
}

.th-hero-slider__thumb {
  max-width: 120px;
  padding: 15px;
  box-sizing: border-box;
  cursor: pointer;
  font-size: 12px;
  line-height: 1.7;
  color: #999999;
  text-transform: uppercase;
}

.th-hero-slider__thumb.swiper-slide-thumb-active {
  color: #000000;
}

@media (min-width: 768px) {
  .th-hero-slider__thumb {
    padding-left: 0;
  }
}

@media (min-width: 992px) {
  .th-hero-slider__thumb {
    font-size: 14px;
  }
}

@media (min-width: 1200px) {
  .th-hero-slider__thumb {
    width: 33.33%;
    max-width: none;
    font-size: 16px;
  }
}

/* 4.67 Awards table*/
.awards-table__inner {
  padding: 40px;
}

@media (min-width: 1200px) {
  .awards-table__inner {
    padding: 120px 80px;
  }
}

@media (min-width: 1560px) {
  .awards-table__inner {
    padding: 120px 110px;
  }
}

.awards-table__heading {
  margin-bottom: 20px;
  font-weight: 400;
}

@media (min-width: 1200px) {
  .awards-table__heading {
    margin-bottom: 72px;
  }
}

.awards-table table {
  width: calc(100% + 80px);
  margin-left: -40px;
}

@media (min-width: 576px) {
  .awards-table table {
    width: 100%;
    margin: 0;
  }
}

.awards-table thead tr {
  border: none;
}

.awards-table tbody tr {
  transition: background-color 0.3s ease;
}

.awards-table tbody tr:hover {
  background-color: #fbfaf6;
}

.awards-table tr {
  border-top: solid 1px #dadada;
}

.awards-table th {
  padding: 14px 5px;
  font-size: 10px;
  line-height: 1;
  color: #999999;
  font-weight: 400;
  text-transform: uppercase;
}

@media (min-width: 576px) {
  .awards-table th {
    padding: 14px 16px;
    font-size: 12px;
    letter-spacing: 0.05em;
  }
}

.awards-table td {
  padding: 16px 5px;
  font-size: 14px;
  line-height: 1;
  color: #000000;
}

@media (min-width: 576px) {
  .awards-table td {
    padding: 30px 16px;
  }
  .awards-table td:first-child {
    text-transform: uppercase;
  }
}

@media (min-width: 1200px) {
  .awards-table td {
    font-size: 18px;
  }
}

.awards-table td a {
  color: #000000;
}

.awards-table td a:hover, .awards-table td a:focus {
  outline: none;
  color: #000000;
  text-decoration: underline;
}

/* 4.xx Technical drawing block */
.technical-drawing {
  width: calc(100% - 16px);
  margin: 8px auto;
  border: solid 4px #000000;
  overflow: hidden;
}

@media (min-width: 1200px) {
  .technical-drawing {
    width: calc(100% - 60px);
    max-width: 1860px;
    margin: 30px auto;
    border-width: 4px;
  }
}

@media (min-width: 992px) {
  .technical-drawing__section--flex {
    display: flex;
  }
}

.technical-drawing__section--flex .technical-drawing__section-column + * {
  border-top: solid 4px #000000;
}

@media (min-width: 992px) {
  .technical-drawing__section--flex .technical-drawing__section-column + * {
    width: 40%;
    border-top: none;
    border-left: solid 1px #000000;
  }
}

.technical-drawing__section + .technical-drawing__section {
  border-top: solid 4px #000000;
}
